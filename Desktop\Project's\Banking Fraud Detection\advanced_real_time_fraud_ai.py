#!/usr/bin/env python3
"""
Advanced Real-Time AI Fraud Detection System
Detects all types of banking frauds with real-time examples and ML models
"""

import json
import datetime
import sqlite3
import numpy as np
import pandas as pd
import re
import hashlib
import uuid
import time
import threading
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

# ML Libraries
from sklearn.ensemble import RandomForestClassifier, IsolationForest, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.svm import SVC
from sklearn.cluster import DBSCAN
import joblib

# Flask and API
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

class FraudCategory(Enum):
    BANKING_FINANCIAL = "banking_financial"
    DIGITAL_PAYMENT = "digital_payment"
    ECOMMERCE = "ecommerce"
    INVESTMENT_PONZI = "investment_ponzi"
    INSURANCE = "insurance"
    IDENTITY_THEFT = "identity_theft"
    CYBER = "cyber"
    GOVERNMENT_SCHEME = "government_scheme"

class SpecificFraudType(Enum):
    # Banking & Financial
    PHISHING = "phishing"
    VISHING = "vishing"
    SKIMMING = "skimming"
    ACCOUNT_TAKEOVER = "account_takeover"
    CHEQUE_FRAUD = "cheque_fraud"
    LOAN_FRAUD = "loan_fraud"
    
    # Digital Payment
    UPI_FRAUD = "upi_fraud"
    WALLET_FRAUD = "wallet_fraud"
    SIM_SWAP = "sim_swap"
    
    # E-commerce
    FAKE_STORE = "fake_store"
    REFUND_FRAUD = "refund_fraud"
    TRIANGULATION_FRAUD = "triangulation_fraud"
    
    # Investment & Ponzi
    CHIT_FUND_SCAM = "chit_fund_scam"
    PONZI_SCHEME = "ponzi_scheme"
    FAKE_IPO = "fake_ipo"
    
    # Insurance
    FALSE_CLAIM = "false_claim"
    POLICY_FORGERY = "policy_forgery"
    
    # Identity Theft
    DOCUMENT_FRAUD = "document_fraud"
    
    # Cyber
    RANSOMWARE = "ransomware"
    BEC_FRAUD = "bec_fraud"
    CRYPTO_SCAM = "crypto_scam"
    
    # Government Scheme
    SUBSIDY_FRAUD = "subsidy_fraud"
    JOB_FRAUD = "job_fraud"

@dataclass
class FraudDetectionResult:
    is_fraud: bool
    fraud_category: FraudCategory
    specific_fraud_types: List[SpecificFraudType]
    confidence_score: float
    risk_score: float
    evidence: List[str]
    recommended_actions: List[str]
    real_time_alerts: List[str]

class AdvancedRealTimeFraudAI:
    def __init__(self):
        self.setup_database()
        self.initialize_ml_models()
        self.load_fraud_patterns()
        self.initialize_real_time_examples()
        
    def setup_database(self):
        """Setup comprehensive database for all fraud types"""
        self.conn = sqlite3.connect('advanced_fraud_ai.db', check_same_thread=False)
        cursor = self.conn.cursor()
        
        # Comprehensive fraud detection table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fraud_detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                detection_id TEXT UNIQUE NOT NULL,
                fraud_category TEXT NOT NULL,
                specific_fraud_types TEXT NOT NULL,
                input_data TEXT NOT NULL,
                confidence_score REAL NOT NULL,
                risk_score REAL NOT NULL,
                evidence TEXT NOT NULL,
                recommended_actions TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_confirmed_fraud INTEGER DEFAULT 0
            )
        ''')
        
        # Communication analysis table (for phishing/vishing)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS communications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                communication_id TEXT UNIQUE NOT NULL,
                communication_type TEXT NOT NULL,
                sender_info TEXT,
                content TEXT NOT NULL,
                language TEXT DEFAULT 'english',
                urgency_score REAL DEFAULT 0.0,
                credential_request_score REAL DEFAULT 0.0,
                is_fraud INTEGER DEFAULT 0,
                fraud_indicators TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Transaction analysis table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id TEXT UNIQUE NOT NULL,
                user_id TEXT NOT NULL,
                amount REAL NOT NULL,
                merchant TEXT,
                payment_method TEXT,
                location TEXT,
                device_info TEXT,
                behavioral_score REAL DEFAULT 0.0,
                anomaly_score REAL DEFAULT 0.0,
                is_fraud INTEGER DEFAULT 0,
                fraud_type TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Identity verification table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS identity_checks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                check_id TEXT UNIQUE NOT NULL,
                document_type TEXT NOT NULL,
                document_number TEXT NOT NULL,
                user_provided_data TEXT NOT NULL,
                verification_score REAL DEFAULT 0.0,
                is_duplicate INTEGER DEFAULT 0,
                is_forged INTEGER DEFAULT 0,
                fraud_indicators TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.conn.commit()
    
    def initialize_ml_models(self):
        """Initialize specialized ML models for different fraud types"""
        
        # Text analysis models for phishing/vishing
        self.text_vectorizer = TfidfVectorizer(max_features=5000, stop_words='english', ngram_range=(1, 3))
        self.phishing_classifier = GradientBoostingClassifier(n_estimators=100, random_state=42)
        
        # Transaction fraud models
        self.transaction_scaler = StandardScaler()
        self.transaction_classifier = RandomForestClassifier(n_estimators=200, random_state=42)
        self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
        
        # Identity verification models
        self.identity_classifier = SVC(probability=True, random_state=42)
        self.identity_scaler = StandardScaler()
        
        # Investment fraud models
        self.investment_vectorizer = CountVectorizer(max_features=1000)
        self.investment_classifier = MultinomialNB()
        
        # Neural network for complex pattern detection
        self.neural_classifier = MLPClassifier(
            hidden_layer_sizes=(200, 100, 50), 
            random_state=42, 
            max_iter=1000
        )
        
        # Train all models
        self.train_all_models()
    
    def train_all_models(self):
        """Train all ML models with comprehensive synthetic data"""
        
        # Train phishing detection model
        phishing_data = self.generate_phishing_training_data()
        X_phish_text, y_phish = phishing_data
        X_phish_vec = self.text_vectorizer.fit_transform(X_phish_text)
        self.phishing_classifier.fit(X_phish_vec, y_phish)
        
        # Train transaction fraud model
        transaction_data = self.generate_transaction_training_data()
        X_trans, y_trans = transaction_data
        X_trans_scaled = self.transaction_scaler.fit_transform(X_trans)
        self.transaction_classifier.fit(X_trans_scaled, y_trans)
        self.anomaly_detector.fit(X_trans_scaled[y_trans == 0])  # Train on normal data
        
        # Train identity verification model
        identity_data = self.generate_identity_training_data()
        X_identity, y_identity = identity_data
        X_identity_scaled = self.identity_scaler.fit_transform(X_identity)
        self.identity_classifier.fit(X_identity_scaled, y_identity)
        
        # Train investment fraud model
        investment_data = self.generate_investment_training_data()
        X_invest_text, y_invest = investment_data
        X_invest_vec = self.investment_vectorizer.fit_transform(X_invest_text)
        self.investment_classifier.fit(X_invest_vec, y_invest)
        
        # Train neural network on combined features
        combined_data = self.generate_combined_training_data()
        X_combined, y_combined = combined_data
        self.neural_classifier.fit(X_combined, y_combined)
        
        print("🤖 All AI models trained successfully!")
    
    def generate_phishing_training_data(self):
        """Generate training data for phishing detection"""
        
        # Legitimate communications
        legitimate = [
            "Your account balance is $1,500. Thank you for banking with us.",
            "Transaction of $50 completed successfully at ABC Store.",
            "Your monthly statement is ready for download in your account.",
            "Loan payment of $300 is due on 15th of this month.",
            "Welcome to our new mobile banking features.",
            "ATM withdrawal of $100 completed at Main Street location.",
            "Direct deposit of $2,000 received in your account.",
            "Your debit card ending in 1234 was used for online purchase.",
            "Account maintenance scheduled for Sunday 2 AM to 4 AM.",
            "New security feature: biometric login now available."
        ]
        
        # Phishing communications with real examples
        phishing = [
            "URGENT: Your account will be CLOSED in 24 hours. Click here to verify immediately or lose access forever.",
            "CONGRATULATIONS! You have WON $50,000 in our lottery. Provide your bank details to claim prize now.",
            "SECURITY ALERT: Suspicious activity detected. Share your OTP: 123456 to secure your account immediately.",
            "Your ATM card is BLOCKED due to security breach. Call ********** and provide PIN to unblock instantly.",
            "IMMEDIATE ACTION REQUIRED: Account compromised. Enter username and password on this link: fake-bank.com",
            "FINAL NOTICE: KYC expired. Update Aadhaar and PAN details within 2 hours or account will be frozen permanently.",
            "LIMITED TIME: Get instant loan of ₹5 lakhs. Just provide Aadhaar, PAN and bank details. No documents needed.",
            "FRAUD PREVENTION: Someone tried to access your account. Confirm your identity by sharing CVV and expiry date.",
            "SYSTEM UPDATE: Enter your internet banking password to continue using our services: update-bank.net",
            "TAX REFUND: You are eligible for ₹25,000 refund. Provide account details for immediate transfer."
        ]
        
        # Vishing (voice phishing) transcripts
        vishing = [
            "Hello sir, I am calling from State Bank security department. Your account shows suspicious activity. Please confirm your debit card number and CVV for verification.",
            "Madam, this is urgent call from RBI. Your account will be suspended due to money laundering suspicion. Provide your internet banking credentials to clear this issue.",
            "Sir, I am calling from your bank's fraud prevention team. Someone is trying to withdraw money from your account. Please share OTP we are sending to stop this transaction.",
            "Hello, this is from bank customer care. Your KYC is incomplete. Please provide Aadhaar number and date of birth to update your profile immediately.",
            "Madam, congratulations! You have won ₹1 lakh in our customer loyalty program. Please share your account number and IFSC code for prize transfer."
        ]
        
        # Create training dataset
        all_texts = legitimate * 50 + phishing * 50 + vishing * 30
        all_labels = [0] * 500 + [1] * 500 + [1] * 150  # 0 = legitimate, 1 = fraud
        
        return all_texts, all_labels
    
    def generate_transaction_training_data(self):
        """Generate training data for transaction fraud detection"""
        np.random.seed(42)
        
        # Features: [amount, hour, day_of_week, merchant_risk, location_risk, velocity, device_risk, behavioral_score]
        normal_transactions = []
        fraud_transactions = []
        
        # Normal transactions
        for _ in range(5000):
            amount = np.random.lognormal(6, 1.5)  # Normal spending patterns
            hour = np.random.normal(14, 4)  # Peak hours around 2 PM
            hour = max(0, min(23, hour))
            day_of_week = np.random.randint(0, 7)
            merchant_risk = np.random.normal(0.2, 0.1)
            location_risk = np.random.normal(0.1, 0.05)
            velocity = np.random.normal(0.3, 0.1)
            device_risk = np.random.normal(0.1, 0.05)
            behavioral_score = np.random.normal(0.2, 0.1)
            
            normal_transactions.append([amount, hour, day_of_week, merchant_risk, 
                                     location_risk, velocity, device_risk, behavioral_score, 0])
        
        # Fraudulent transactions with specific patterns
        fraud_patterns = [
            # High amount fraud
            {'amount_mult': 10, 'hour_shift': 0, 'merchant_risk': 0.9, 'location_risk': 0.8},
            # Unusual time fraud
            {'amount_mult': 3, 'hour_shift': 20, 'merchant_risk': 0.7, 'location_risk': 0.6},
            # Rapid transaction fraud
            {'amount_mult': 2, 'hour_shift': 0, 'merchant_risk': 0.8, 'location_risk': 0.9},
            # Device-based fraud
            {'amount_mult': 5, 'hour_shift': 0, 'merchant_risk': 0.6, 'location_risk': 0.95}
        ]
        
        for pattern in fraud_patterns:
            for _ in range(300):
                amount = np.random.lognormal(6, 1) * pattern['amount_mult']
                hour = (np.random.normal(14, 4) + pattern['hour_shift']) % 24
                day_of_week = np.random.randint(0, 7)
                merchant_risk = pattern['merchant_risk'] + np.random.normal(0, 0.1)
                location_risk = pattern['location_risk'] + np.random.normal(0, 0.1)
                velocity = np.random.normal(0.8, 0.1)
                device_risk = np.random.normal(0.7, 0.1)
                behavioral_score = np.random.normal(0.8, 0.1)
                
                fraud_transactions.append([amount, hour, day_of_week, merchant_risk,
                                         location_risk, velocity, device_risk, behavioral_score, 1])
        
        all_data = np.array(normal_transactions + fraud_transactions)
        X = all_data[:, :-1]
        y = all_data[:, -1]
        
        return X, y
    
    def generate_identity_training_data(self):
        """Generate training data for identity verification"""
        np.random.seed(42)
        
        # Features: [doc_format_score, duplicate_score, consistency_score, biometric_score, cross_ref_score]
        legitimate_identities = []
        fraudulent_identities = []
        
        # Legitimate identities
        for _ in range(2000):
            doc_format_score = np.random.normal(0.9, 0.1)
            duplicate_score = np.random.normal(0.1, 0.05)
            consistency_score = np.random.normal(0.9, 0.1)
            biometric_score = np.random.normal(0.85, 0.1)
            cross_ref_score = np.random.normal(0.9, 0.1)
            
            legitimate_identities.append([doc_format_score, duplicate_score, consistency_score,
                                        biometric_score, cross_ref_score, 0])
        
        # Fraudulent identities
        for _ in range(500):
            doc_format_score = np.random.normal(0.4, 0.2)  # Poor format
            duplicate_score = np.random.normal(0.8, 0.1)   # High duplication
            consistency_score = np.random.normal(0.3, 0.2) # Inconsistent
            biometric_score = np.random.normal(0.2, 0.1)   # Poor biometric match
            cross_ref_score = np.random.normal(0.1, 0.1)   # Poor cross-reference
            
            fraudulent_identities.append([doc_format_score, duplicate_score, consistency_score,
                                         biometric_score, cross_ref_score, 1])
        
        all_data = np.array(legitimate_identities + fraudulent_identities)
        X = all_data[:, :-1]
        y = all_data[:, -1]
        
        return X, y
    
    def generate_investment_training_data(self):
        """Generate training data for investment fraud detection"""
        
        # Legitimate investment communications
        legitimate_investments = [
            "Mutual fund returns based on market performance. Past performance does not guarantee future results.",
            "Government bonds offering 6% annual return with capital protection and tax benefits.",
            "Diversified equity portfolio managed by certified financial advisors with 10-year track record.",
            "Fixed deposit offering 7% interest rate for 5-year term with quarterly interest payout option.",
            "SIP investment in blue-chip stocks with systematic risk management and professional fund management.",
            "Real estate investment trust with transparent quarterly reports and regulatory compliance.",
            "Gold ETF investment with low expense ratio and high liquidity for portfolio diversification.",
            "Corporate bonds rated AAA by credit agencies with semi-annual interest payments."
        ]
        
        # Fraudulent investment schemes
        fraudulent_investments = [
            "GUARANTEED 50% returns in 6 months! No risk, only profit. Limited time offer for smart investors.",
            "Double your money in 90 days with our secret trading algorithm. 100% success rate guaranteed!",
            "Join our exclusive investment club. Minimum ₹1 lakh investment returns ₹10 lakhs in 1 year. Risk-free!",
            "Cryptocurrency mining pool offering 200% returns monthly. Get rich quick with Bitcoin investment!",
            "Chit fund scheme: Invest ₹50,000 monthly, get ₹50 lakhs after 2 years. Government approved program.",
            "Foreign exchange trading with 1000% profit guarantee. Our expert traders never lose money.",
            "Real estate project in Dubai offering 300% returns. Book now with just ₹10,000 advance payment.",
            "IPO investment opportunity: Company going public next month. Guaranteed 500% profit on listing day.",
            "Ponzi scheme disguised as mutual fund. Refer 5 friends and get bonus returns. Limited seats available.",
            "Fake stock tips: Buy these 3 stocks today and become millionaire in 6 months. Insider information!"
        ]
        
        # Create training dataset
        all_texts = legitimate_investments * 100 + fraudulent_investments * 100
        all_labels = [0] * 800 + [1] * 1000  # 0 = legitimate, 1 = fraud
        
        return all_texts, all_labels
    
    def generate_combined_training_data(self):
        """Generate combined training data for neural network"""
        np.random.seed(42)
        
        # Combined features from all fraud types
        # [text_sentiment, amount_risk, time_risk, location_risk, device_risk, behavioral_risk, identity_risk]
        
        normal_patterns = []
        fraud_patterns = []
        
        # Normal patterns
        for _ in range(3000):
            text_sentiment = np.random.normal(0.2, 0.1)
            amount_risk = np.random.normal(0.2, 0.1)
            time_risk = np.random.normal(0.1, 0.05)
            location_risk = np.random.normal(0.1, 0.05)
            device_risk = np.random.normal(0.1, 0.05)
            behavioral_risk = np.random.normal(0.2, 0.1)
            identity_risk = np.random.normal(0.1, 0.05)
            
            normal_patterns.append([text_sentiment, amount_risk, time_risk, location_risk,
                                  device_risk, behavioral_risk, identity_risk, 0])
        
        # Fraud patterns
        for _ in range(1000):
            text_sentiment = np.random.normal(0.8, 0.1)
            amount_risk = np.random.normal(0.7, 0.1)
            time_risk = np.random.normal(0.6, 0.1)
            location_risk = np.random.normal(0.8, 0.1)
            device_risk = np.random.normal(0.7, 0.1)
            behavioral_risk = np.random.normal(0.8, 0.1)
            identity_risk = np.random.normal(0.9, 0.1)
            
            fraud_patterns.append([text_sentiment, amount_risk, time_risk, location_risk,
                                 device_risk, behavioral_risk, identity_risk, 1])
        
        all_data = np.array(normal_patterns + fraud_patterns)
        X = all_data[:, :-1]
        y = all_data[:, -1]
        
        return X, y

    def load_fraud_patterns(self):
        """Load known fraud patterns and indicators"""
        self.fraud_patterns = {
            'phishing_keywords': [
                'urgent', 'immediate', 'expire', 'suspend', 'block', 'freeze', 'verify',
                'click here', 'act now', 'limited time', 'congratulations', 'winner',
                'prize', 'lottery', 'claim', 'otp', 'pin', 'password', 'cvv'
            ],
            'vishing_indicators': [
                'security department', 'fraud prevention', 'rbi', 'urgent call',
                'account suspended', 'money laundering', 'confirm identity',
                'share otp', 'provide cvv', 'customer loyalty'
            ],
            'upi_fraud_patterns': [
                'unknown merchant', 'qr code scam', 'fake payment request',
                'wrong beneficiary', 'suspicious amount', 'rapid transactions'
            ],
            'investment_fraud_keywords': [
                'guaranteed returns', 'risk-free', 'double money', 'get rich quick',
                'secret algorithm', 'insider information', 'limited time offer',
                'no risk only profit', '100% success rate', 'become millionaire'
            ],
            'identity_theft_indicators': [
                'duplicate documents', 'forged signature', 'inconsistent data',
                'multiple accounts', 'stolen credentials', 'fake biometrics'
            ]
        }

    def initialize_real_time_examples(self):
        """Initialize real-time fraud examples for testing"""
        self.real_time_examples = {
            'phishing_email': {
                'content': "URGENT: Your account will be closed in 24 hours. Click here to verify: fake-bank.com/verify",
                'sender': "<EMAIL>",
                'expected_result': "HIGH RISK - Phishing detected"
            },
            'vishing_call': {
                'transcript': "Hello sir, I am calling from bank security. Your account shows suspicious activity. Please share your debit card CVV for verification.",
                'caller_id': "+91-**********",
                'expected_result': "HIGH RISK - Vishing attempt"
            },
            'upi_fraud': {
                'transaction': {
                    'amount': 50000,
                    'merchant': 'Unknown QR Code',
                    'payment_method': 'upi',
                    'time': '02:30 AM'
                },
                'expected_result': "HIGH RISK - UPI fraud"
            },
            'account_takeover': {
                'login_attempt': {
                    'location': 'Foreign Country',
                    'device': 'New Device',
                    'time': '03:00 AM',
                    'failed_attempts': 5
                },
                'expected_result': "HIGH RISK - Account takeover"
            },
            'investment_scam': {
                'offer': "GUARANTEED 500% returns in 6 months! No risk, only profit. Join our exclusive investment club now!",
                'expected_result': "HIGH RISK - Investment fraud"
            }
        }

    # SPECIFIC FRAUD DETECTION METHODS

    def detect_phishing_fraud(self, communication_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect phishing attempts in emails/SMS"""
        content = communication_data.get('content', '').lower()
        sender = communication_data.get('sender', '').lower()

        evidence = []
        risk_score = 0.0

        # Check for phishing keywords
        phishing_keywords = self.fraud_patterns['phishing_keywords']
        keyword_count = sum(1 for keyword in phishing_keywords if keyword in content)
        if keyword_count > 0:
            risk_score += min(keyword_count * 0.15, 0.6)
            evidence.append(f"Contains {keyword_count} phishing keywords")

        # Check for urgency indicators
        urgency_words = ['urgent', 'immediate', 'expire', 'suspend', 'block']
        urgency_count = sum(1 for word in urgency_words if word in content)
        if urgency_count > 0:
            risk_score += 0.3
            evidence.append(f"High urgency language detected ({urgency_count} indicators)")

        # Check for credential requests
        credential_requests = ['otp', 'pin', 'password', 'cvv', 'card number', 'account number']
        cred_count = sum(1 for cred in credential_requests if cred in content)
        if cred_count > 0:
            risk_score += 0.4
            evidence.append(f"Requests {cred_count} types of credentials")

        # Check sender authenticity
        if 'bank' in sender and not self.is_legitimate_bank_domain(sender):
            risk_score += 0.5
            evidence.append("Suspicious sender impersonating bank")

        # ML model prediction
        if hasattr(self.text_vectorizer, 'vocabulary_'):
            text_vector = self.text_vectorizer.transform([content])
            ml_prediction = self.phishing_classifier.predict_proba(text_vector)[0][1]
            risk_score += ml_prediction * 0.4
            evidence.append(f"ML model confidence: {ml_prediction:.2f}")

        is_fraud = risk_score > 0.6
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_vishing_fraud(self, call_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect voice phishing (vishing) attempts"""
        transcript = call_data.get('transcript', '').lower()
        caller_id = call_data.get('caller_id', '')

        evidence = []
        risk_score = 0.0

        # Check for vishing indicators
        vishing_indicators = self.fraud_patterns['vishing_indicators']
        indicator_count = sum(1 for indicator in vishing_indicators if indicator in transcript)
        if indicator_count > 0:
            risk_score += min(indicator_count * 0.2, 0.7)
            evidence.append(f"Contains {indicator_count} vishing indicators")

        # Check for impersonation
        impersonation_phrases = ['calling from bank', 'security department', 'fraud prevention', 'rbi']
        if any(phrase in transcript for phrase in impersonation_phrases):
            risk_score += 0.4
            evidence.append("Impersonating bank/authority")

        # Check for credential requests in voice
        if any(word in transcript for word in ['cvv', 'pin', 'password', 'otp', 'card number']):
            risk_score += 0.5
            evidence.append("Requesting sensitive information over phone")

        # Check caller ID authenticity
        if not self.is_legitimate_bank_number(caller_id):
            risk_score += 0.3
            evidence.append("Suspicious caller ID")

        is_fraud = risk_score > 0.6
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_upi_fraud(self, transaction_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect UPI-specific fraud patterns"""
        amount = float(transaction_data.get('amount', 0))
        merchant = transaction_data.get('merchant', '').lower()
        payment_method = transaction_data.get('payment_method', '').lower()
        time_str = transaction_data.get('time', '')

        evidence = []
        risk_score = 0.0

        if 'upi' not in payment_method:
            return False, 0.0, ["Not a UPI transaction"]

        # Check for suspicious merchants
        suspicious_merchants = ['unknown', 'qr code', 'temp', 'test', 'fake']
        if any(sus in merchant for sus in suspicious_merchants):
            risk_score += 0.4
            evidence.append(f"Suspicious merchant: {merchant}")

        # Check for unusual amounts
        if amount > 50000:  # High amount for UPI
            risk_score += 0.3
            evidence.append(f"High amount for UPI: ₹{amount:,.2f}")

        # Check for round amounts (common in fraud)
        if amount % 1000 == 0 and amount > 5000:
            risk_score += 0.2
            evidence.append(f"Suspicious round amount: ₹{amount:,.2f}")

        # Check for unusual timing
        if self.is_unusual_time(time_str):
            risk_score += 0.3
            evidence.append(f"Transaction at unusual time: {time_str}")

        # Check for QR code fraud patterns
        if 'qr' in merchant or 'scan' in merchant:
            risk_score += 0.2
            evidence.append("QR code transaction - higher risk")

        is_fraud = risk_score > 0.5
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_account_takeover(self, login_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect account takeover attempts"""
        location = login_data.get('location', '').lower()
        device = login_data.get('device', '').lower()
        time_str = login_data.get('time', '')
        failed_attempts = login_data.get('failed_attempts', 0)

        evidence = []
        risk_score = 0.0

        # Check for multiple failed attempts
        if failed_attempts > 3:
            risk_score += min(failed_attempts * 0.1, 0.5)
            evidence.append(f"Multiple failed login attempts: {failed_attempts}")

        # Check for foreign/unusual location
        suspicious_locations = ['foreign', 'unknown', 'vpn', 'proxy', 'tor']
        if any(loc in location for loc in suspicious_locations):
            risk_score += 0.4
            evidence.append(f"Suspicious location: {location}")

        # Check for new/unknown device
        if 'new' in device or 'unknown' in device:
            risk_score += 0.3
            evidence.append(f"New/unknown device: {device}")

        # Check for unusual timing
        if self.is_unusual_time(time_str):
            risk_score += 0.2
            evidence.append(f"Login at unusual time: {time_str}")

        # Check for impossible travel
        if self.detect_impossible_travel(login_data):
            risk_score += 0.6
            evidence.append("Impossible travel detected")

        is_fraud = risk_score > 0.6
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_investment_fraud(self, investment_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect investment and Ponzi scheme fraud"""
        offer_text = investment_data.get('offer', '').lower()
        returns_promised = investment_data.get('returns_percentage', 0)
        time_period = investment_data.get('time_period_months', 12)

        evidence = []
        risk_score = 0.0

        # Check for fraud keywords
        fraud_keywords = self.fraud_patterns['investment_fraud_keywords']
        keyword_count = sum(1 for keyword in fraud_keywords if keyword in offer_text)
        if keyword_count > 0:
            risk_score += min(keyword_count * 0.2, 0.7)
            evidence.append(f"Contains {keyword_count} fraud indicators")

        # Check for unrealistic returns
        annual_return = (returns_promised / time_period) * 12
        if annual_return > 50:  # More than 50% annual return
            risk_score += 0.5
            evidence.append(f"Unrealistic returns: {annual_return:.1f}% annually")

        # Check for guarantee claims
        guarantee_words = ['guaranteed', 'risk-free', '100% success', 'no risk']
        if any(word in offer_text for word in guarantee_words):
            risk_score += 0.4
            evidence.append("Claims guaranteed/risk-free returns")

        # Check for urgency/pressure tactics
        pressure_words = ['limited time', 'act now', 'hurry', 'last chance']
        if any(word in offer_text for word in pressure_words):
            risk_score += 0.3
            evidence.append("Uses pressure tactics")

        # ML model prediction for investment text
        if hasattr(self.investment_vectorizer, 'vocabulary_'):
            text_vector = self.investment_vectorizer.transform([offer_text])
            ml_prediction = self.investment_classifier.predict_proba(text_vector)[0][1]
            risk_score += ml_prediction * 0.4
            evidence.append(f"ML investment model confidence: {ml_prediction:.2f}")

        is_fraud = risk_score > 0.6
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_identity_theft(self, identity_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect identity theft and document fraud"""
        document_type = identity_data.get('document_type', '').lower()
        document_number = identity_data.get('document_number', '')
        user_data = identity_data.get('user_data', {})

        evidence = []
        risk_score = 0.0

        # Check document format
        if document_type == 'aadhaar':
            if not self.validate_aadhaar_format(document_number):
                risk_score += 0.4
                evidence.append("Invalid Aadhaar format")
        elif document_type == 'pan':
            if not self.validate_pan_format(document_number):
                risk_score += 0.4
                evidence.append("Invalid PAN format")

        # Check for duplicate usage
        if self.check_document_duplication(document_type, document_number):
            risk_score += 0.6
            evidence.append("Document used by multiple accounts")

        # Check data consistency
        consistency_score = self.check_data_consistency(user_data)
        if consistency_score < 0.5:
            risk_score += 0.3
            evidence.append(f"Data inconsistency detected (score: {consistency_score:.2f})")

        # ML model prediction for identity verification
        identity_features = self.extract_identity_features(identity_data)
        if identity_features is not None:
            identity_scaled = self.identity_scaler.transform([identity_features])
            ml_prediction = self.identity_classifier.predict_proba(identity_scaled)[0][1]
            risk_score += ml_prediction * 0.4
            evidence.append(f"ML identity model confidence: {ml_prediction:.2f}")

        is_fraud = risk_score > 0.6
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_sim_swap_fraud(self, transaction_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect SIM swap fraud indicators"""
        user_id = transaction_data.get('user_id', '')
        phone_number = transaction_data.get('phone_number', '')
        otp_requests = transaction_data.get('otp_requests_count', 0)

        evidence = []
        risk_score = 0.0

        # Check for recent phone number changes
        if self.check_recent_phone_change(user_id):
            risk_score += 0.6
            evidence.append("Recent phone number change detected")

        # Check for excessive OTP requests
        if otp_requests > 5:
            risk_score += 0.4
            evidence.append(f"Excessive OTP requests: {otp_requests}")

        # Check for location mismatch with phone registration
        if self.check_location_phone_mismatch(transaction_data):
            risk_score += 0.3
            evidence.append("Location mismatch with phone registration")

        is_fraud = risk_score > 0.7
        return is_fraud, min(risk_score, 1.0), evidence

    def detect_crypto_scam(self, crypto_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect cryptocurrency scams"""
        platform_name = crypto_data.get('platform_name', '').lower()
        promised_returns = crypto_data.get('promised_returns', 0)
        investment_amount = crypto_data.get('investment_amount', 0)

        evidence = []
        risk_score = 0.0

        # Check for fake crypto platforms
        suspicious_platforms = ['fake', 'scam', 'quick', 'instant', 'guaranteed']
        if any(sus in platform_name for sus in suspicious_platforms):
            risk_score += 0.5
            evidence.append(f"Suspicious platform name: {platform_name}")

        # Check for unrealistic returns
        if promised_returns > 100:  # More than 100% returns
            risk_score += 0.6
            evidence.append(f"Unrealistic crypto returns: {promised_returns}%")

        # Check for high investment pressure
        if investment_amount > 100000:  # High investment amount
            risk_score += 0.3
            evidence.append(f"High investment amount: ₹{investment_amount:,.2f}")

        is_fraud = risk_score > 0.6
        return is_fraud, min(risk_score, 1.0), evidence

    # HELPER METHODS

    def is_legitimate_bank_domain(self, sender: str) -> bool:
        """Check if sender domain is legitimate bank domain"""
        legitimate_domains = [
            '@sbi.co.in', '@hdfcbank.com', '@icicibank.com', '@axisbank.com',
            '@kotak.com', '@pnb.co.in', '@bankofbaroda.com', '@canarabank.com'
        ]
        return any(domain in sender for domain in legitimate_domains)

    def is_legitimate_bank_number(self, caller_id: str) -> bool:
        """Check if caller ID is legitimate bank number"""
        # Simplified check - in real implementation, use verified bank number database
        legitimate_patterns = ['1800', '1860', '+91-1800', '+91-1860']
        return any(pattern in caller_id for pattern in legitimate_patterns)

    def is_unusual_time(self, time_str: str) -> bool:
        """Check if transaction time is unusual"""
        try:
            if ':' in time_str:
                hour = int(time_str.split(':')[0])
                # Consider 11 PM to 6 AM as unusual
                return hour >= 23 or hour <= 6
        except:
            pass
        return False

    def detect_impossible_travel(self, data: Dict) -> bool:
        """Detect impossible travel between locations"""
        # Simplified implementation - in real scenario, use geolocation APIs
        current_location = data.get('location', '').lower()
        previous_location = data.get('previous_location', '').lower()

        if current_location != previous_location:
            # If locations are different countries/continents
            if any(loc in current_location for loc in ['foreign', 'international', 'overseas']):
                return True
        return False

    def validate_aadhaar_format(self, aadhaar: str) -> bool:
        """Validate Aadhaar number format"""
        return bool(re.match(r'^\d{12}$', aadhaar))

    def validate_pan_format(self, pan: str) -> bool:
        """Validate PAN number format"""
        return bool(re.match(r'^[A-Z]{5}\d{4}[A-Z]$', pan))

    def check_document_duplication(self, doc_type: str, doc_number: str) -> bool:
        """Check if document is used by multiple accounts"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT COUNT(DISTINCT user_provided_data) FROM identity_checks
            WHERE document_type = ? AND document_number = ?
        ''', (doc_type, doc_number))

        count = cursor.fetchone()[0]
        return count > 1

    def check_data_consistency(self, user_data: Dict) -> float:
        """Check consistency of user provided data"""
        # Simplified consistency check
        required_fields = ['name', 'address', 'phone', 'email']
        provided_fields = sum(1 for field in required_fields if user_data.get(field))

        consistency_score = provided_fields / len(required_fields)

        # Additional checks for data format consistency
        if user_data.get('email') and '@' not in user_data.get('email', ''):
            consistency_score -= 0.2

        if user_data.get('phone') and not re.match(r'^\+?[\d\s\-\(\)]{10,15}$', user_data.get('phone', '')):
            consistency_score -= 0.2

        return max(0, consistency_score)

    def extract_identity_features(self, identity_data: Dict) -> Optional[List[float]]:
        """Extract features for identity verification ML model"""
        try:
            doc_format_score = 1.0 if self.validate_aadhaar_format(identity_data.get('document_number', '')) else 0.0
            duplicate_score = 1.0 if self.check_document_duplication(
                identity_data.get('document_type', ''),
                identity_data.get('document_number', '')
            ) else 0.0
            consistency_score = self.check_data_consistency(identity_data.get('user_data', {}))
            biometric_score = identity_data.get('biometric_match_score', 0.5)
            cross_ref_score = identity_data.get('cross_reference_score', 0.5)

            return [doc_format_score, duplicate_score, consistency_score, biometric_score, cross_ref_score]
        except:
            return None

    def check_recent_phone_change(self, user_id: str) -> bool:
        """Check if user recently changed phone number"""
        # Simplified check - in real implementation, check user activity logs
        return False  # Placeholder

    def check_location_phone_mismatch(self, data: Dict) -> bool:
        """Check if transaction location mismatches phone registration location"""
        # Simplified check - in real implementation, use telecom provider APIs
        return False  # Placeholder

    # MAIN COMPREHENSIVE FRAUD DETECTION METHOD

    def comprehensive_fraud_detection(self, input_data: Dict) -> FraudDetectionResult:
        """Main method for comprehensive fraud detection across all types"""

        detected_fraud_types = []
        all_evidence = []
        max_risk_score = 0.0
        max_confidence = 0.0
        fraud_category = None

        analysis_type = input_data.get('analysis_type', 'comprehensive')

        # 1. BANKING & FINANCIAL FRAUDS
        if analysis_type in ['comprehensive', 'banking', 'communication']:
            # Phishing Detection
            if 'content' in input_data:
                is_phishing, phish_risk, phish_evidence = self.detect_phishing_fraud(input_data)
                if is_phishing:
                    detected_fraud_types.append(SpecificFraudType.PHISHING)
                    all_evidence.extend([f"PHISHING: {e}" for e in phish_evidence])
                    max_risk_score = max(max_risk_score, phish_risk * 100)
                    fraud_category = FraudCategory.BANKING_FINANCIAL

            # Vishing Detection
            if 'transcript' in input_data:
                is_vishing, vish_risk, vish_evidence = self.detect_vishing_fraud(input_data)
                if is_vishing:
                    detected_fraud_types.append(SpecificFraudType.VISHING)
                    all_evidence.extend([f"VISHING: {e}" for e in vish_evidence])
                    max_risk_score = max(max_risk_score, vish_risk * 100)
                    fraud_category = FraudCategory.BANKING_FINANCIAL

        # 2. DIGITAL PAYMENT FRAUDS
        if analysis_type in ['comprehensive', 'transaction', 'payment']:
            # UPI Fraud Detection
            if 'payment_method' in input_data:
                is_upi_fraud, upi_risk, upi_evidence = self.detect_upi_fraud(input_data)
                if is_upi_fraud:
                    detected_fraud_types.append(SpecificFraudType.UPI_FRAUD)
                    all_evidence.extend([f"UPI FRAUD: {e}" for e in upi_evidence])
                    max_risk_score = max(max_risk_score, upi_risk * 100)
                    fraud_category = FraudCategory.DIGITAL_PAYMENT

            # SIM Swap Detection
            if 'phone_number' in input_data:
                is_sim_swap, sim_risk, sim_evidence = self.detect_sim_swap_fraud(input_data)
                if is_sim_swap:
                    detected_fraud_types.append(SpecificFraudType.SIM_SWAP)
                    all_evidence.extend([f"SIM SWAP: {e}" for e in sim_evidence])
                    max_risk_score = max(max_risk_score, sim_risk * 100)
                    fraud_category = FraudCategory.DIGITAL_PAYMENT

        # 3. ACCOUNT SECURITY
        if analysis_type in ['comprehensive', 'login', 'security']:
            # Account Takeover Detection
            if 'location' in input_data or 'device' in input_data:
                is_takeover, takeover_risk, takeover_evidence = self.detect_account_takeover(input_data)
                if is_takeover:
                    detected_fraud_types.append(SpecificFraudType.ACCOUNT_TAKEOVER)
                    all_evidence.extend([f"ACCOUNT TAKEOVER: {e}" for e in takeover_evidence])
                    max_risk_score = max(max_risk_score, takeover_risk * 100)
                    fraud_category = FraudCategory.BANKING_FINANCIAL

        # 4. INVESTMENT & PONZI SCHEMES
        if analysis_type in ['comprehensive', 'investment']:
            # Investment Fraud Detection
            if 'offer' in input_data or 'returns_percentage' in input_data:
                is_investment_fraud, invest_risk, invest_evidence = self.detect_investment_fraud(input_data)
                if is_investment_fraud:
                    detected_fraud_types.append(SpecificFraudType.PONZI_SCHEME)
                    all_evidence.extend([f"INVESTMENT FRAUD: {e}" for e in invest_evidence])
                    max_risk_score = max(max_risk_score, invest_risk * 100)
                    fraud_category = FraudCategory.INVESTMENT_PONZI

        # 5. IDENTITY THEFT
        if analysis_type in ['comprehensive', 'identity']:
            # Identity Theft Detection
            if 'document_type' in input_data:
                is_identity_theft, identity_risk, identity_evidence = self.detect_identity_theft(input_data)
                if is_identity_theft:
                    detected_fraud_types.append(SpecificFraudType.DOCUMENT_FRAUD)
                    all_evidence.extend([f"IDENTITY THEFT: {e}" for e in identity_evidence])
                    max_risk_score = max(max_risk_score, identity_risk * 100)
                    fraud_category = FraudCategory.IDENTITY_THEFT

        # 6. CRYPTO SCAMS
        if analysis_type in ['comprehensive', 'crypto']:
            # Crypto Scam Detection
            if 'platform_name' in input_data:
                is_crypto_scam, crypto_risk, crypto_evidence = self.detect_crypto_scam(input_data)
                if is_crypto_scam:
                    detected_fraud_types.append(SpecificFraudType.CRYPTO_SCAM)
                    all_evidence.extend([f"CRYPTO SCAM: {e}" for e in crypto_evidence])
                    max_risk_score = max(max_risk_score, crypto_risk * 100)
                    fraud_category = FraudCategory.CYBER

        # Generate comprehensive recommendations
        recommended_actions = self.generate_comprehensive_recommendations(
            detected_fraud_types, max_risk_score, fraud_category
        )

        # Generate real-time alerts
        real_time_alerts = self.generate_real_time_alerts(
            detected_fraud_types, max_risk_score
        )

        # Store detection result
        self.store_fraud_detection_result(input_data, detected_fraud_types, max_risk_score, all_evidence)

        # Calculate overall confidence
        max_confidence = min(max_risk_score / 100, 1.0)

        return FraudDetectionResult(
            is_fraud=len(detected_fraud_types) > 0,
            fraud_category=fraud_category or FraudCategory.BANKING_FINANCIAL,
            specific_fraud_types=detected_fraud_types,
            confidence_score=max_confidence,
            risk_score=max_risk_score,
            evidence=all_evidence,
            recommended_actions=recommended_actions,
            real_time_alerts=real_time_alerts
        )

    def generate_comprehensive_recommendations(self, fraud_types: List[SpecificFraudType],
                                             risk_score: float, category: FraudCategory) -> List[str]:
        """Generate comprehensive recommendations based on detected fraud types"""
        recommendations = []

        # Risk-based recommendations
        if risk_score > 90:
            recommendations.append("🚨 CRITICAL: Immediately block all transactions and freeze account")
            recommendations.append("📞 Contact customer via verified phone number within 5 minutes")
            recommendations.append("🔒 Escalate to fraud investigation team immediately")
            recommendations.append("📋 File incident report with law enforcement if confirmed")
        elif risk_score > 70:
            recommendations.append("⚠️ HIGH RISK: Hold transaction for manual review")
            recommendations.append("📧 Send immediate security alert to customer")
            recommendations.append("🔐 Require additional authentication for next 24 hours")
            recommendations.append("👥 Notify fraud prevention team")
        elif risk_score > 50:
            recommendations.append("⚡ MEDIUM RISK: Monitor account for suspicious activity")
            recommendations.append("📝 Log incident for pattern analysis")
            recommendations.append("🔔 Send security notification to customer")

        # Fraud-type specific recommendations
        if SpecificFraudType.PHISHING in fraud_types:
            recommendations.append("📧 Block sender and report phishing attempt to authorities")
            recommendations.append("📚 Send phishing awareness education to customer")
            recommendations.append("🛡️ Enable enhanced email security filters")

        if SpecificFraudType.VISHING in fraud_types:
            recommendations.append("📞 Add caller ID to fraud database")
            recommendations.append("🎓 Provide voice phishing awareness training")
            recommendations.append("📋 Report to telecom fraud prevention")

        if SpecificFraudType.UPI_FRAUD in fraud_types:
            recommendations.append("💳 Temporarily disable UPI transactions")
            recommendations.append("🔍 Verify merchant authenticity")
            recommendations.append("📱 Check for malicious apps on customer device")

        if SpecificFraudType.ACCOUNT_TAKEOVER in fraud_types:
            recommendations.append("🔑 Force password reset and logout all sessions")
            recommendations.append("📱 Enable two-factor authentication")
            recommendations.append("🌐 Block access from suspicious locations")

        if SpecificFraudType.SIM_SWAP in fraud_types:
            recommendations.append("📞 Verify phone number change with telecom provider")
            recommendations.append("🚫 Temporarily disable SMS-based authentication")
            recommendations.append("🔐 Switch to app-based authentication")

        if SpecificFraudType.DOCUMENT_FRAUD in fraud_types:
            recommendations.append("📄 Request additional identity verification documents")
            recommendations.append("🏛️ Cross-check with government databases")
            recommendations.append("👤 Require in-person verification")

        if SpecificFraudType.PONZI_SCHEME in fraud_types:
            recommendations.append("💰 Block investment transactions immediately")
            recommendations.append("📢 Report to financial regulatory authorities")
            recommendations.append("📚 Provide investment fraud education")

        if SpecificFraudType.CRYPTO_SCAM in fraud_types:
            recommendations.append("₿ Block cryptocurrency-related transactions")
            recommendations.append("🔍 Investigate platform legitimacy")
            recommendations.append("📊 Report to crypto fraud databases")

        return recommendations

    def generate_real_time_alerts(self, fraud_types: List[SpecificFraudType],
                                 risk_score: float) -> List[str]:
        """Generate real-time alerts for immediate action"""
        alerts = []

        if risk_score > 80:
            alerts.append("🚨 FRAUD ALERT: High-risk activity detected - IMMEDIATE ACTION REQUIRED")
            alerts.append("🔒 Transaction automatically blocked for security")
            alerts.append("📞 Customer service team notified for immediate contact")

        for fraud_type in fraud_types:
            if fraud_type == SpecificFraudType.PHISHING:
                alerts.append("📧 PHISHING ALERT: Malicious communication detected")
            elif fraud_type == SpecificFraudType.VISHING:
                alerts.append("📞 VISHING ALERT: Fraudulent voice call detected")
            elif fraud_type == SpecificFraudType.UPI_FRAUD:
                alerts.append("💳 UPI FRAUD ALERT: Suspicious payment detected")
            elif fraud_type == SpecificFraudType.ACCOUNT_TAKEOVER:
                alerts.append("🔐 SECURITY BREACH: Account takeover attempt detected")
            elif fraud_type == SpecificFraudType.SIM_SWAP:
                alerts.append("📱 SIM SWAP ALERT: Mobile number compromise detected")
            elif fraud_type == SpecificFraudType.DOCUMENT_FRAUD:
                alerts.append("📄 IDENTITY FRAUD: Document forgery detected")
            elif fraud_type == SpecificFraudType.PONZI_SCHEME:
                alerts.append("💰 INVESTMENT FRAUD: Ponzi scheme detected")
            elif fraud_type == SpecificFraudType.CRYPTO_SCAM:
                alerts.append("₿ CRYPTO SCAM: Fraudulent cryptocurrency platform detected")

        return alerts

    def store_fraud_detection_result(self, input_data: Dict, fraud_types: List[SpecificFraudType],
                                   risk_score: float, evidence: List[str]):
        """Store fraud detection result in database"""
        try:
            cursor = self.conn.cursor()
            detection_id = str(uuid.uuid4()).replace('-', '').upper()[:12]

            cursor.execute('''
                INSERT INTO fraud_detections (
                    detection_id, fraud_category, specific_fraud_types, input_data,
                    confidence_score, risk_score, evidence, recommended_actions
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                detection_id,
                'multiple' if len(fraud_types) > 1 else fraud_types[0].value if fraud_types else 'none',
                json.dumps([ft.value for ft in fraud_types]),
                json.dumps(input_data),
                min(risk_score / 100, 1.0),
                risk_score,
                json.dumps(evidence),
                json.dumps([])  # Recommendations stored separately
            ))

            self.conn.commit()
        except Exception as e:
            print(f"Error storing fraud detection result: {e}")

# Initialize the advanced fraud detection system
fraud_ai = AdvancedRealTimeFraudAI()

# Flask API Endpoints with Real-Time Examples

@app.route('/api/detect_fraud', methods=['POST'])
def detect_fraud():
    """Main endpoint for comprehensive fraud detection"""
    try:
        input_data = request.json

        if not input_data:
            return jsonify({'error': 'No input data provided'}), 400

        # Perform comprehensive fraud detection
        result = fraud_ai.comprehensive_fraud_detection(input_data)

        # Convert enum values to strings for JSON serialization
        response = {
            'is_fraud': result.is_fraud,
            'fraud_category': result.fraud_category.value,
            'specific_fraud_types': [ft.value for ft in result.specific_fraud_types],
            'confidence_score': result.confidence_score,
            'risk_score': result.risk_score,
            'evidence': result.evidence,
            'recommended_actions': result.recommended_actions,
            'real_time_alerts': result.real_time_alerts,
            'timestamp': datetime.datetime.now().isoformat()
        }

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test_examples', methods=['GET'])
def get_test_examples():
    """Get real-time fraud examples for testing"""
    return jsonify({
        'examples': fraud_ai.real_time_examples,
        'description': 'Use these examples to test different fraud detection scenarios'
    })

@app.route('/api/test_phishing', methods=['POST'])
def test_phishing():
    """Test phishing detection with real example"""
    try:
        # Use real phishing example
        phishing_example = {
            'analysis_type': 'communication',
            'content': "URGENT: Your account will be closed in 24 hours due to suspicious activity. Click here to verify your identity immediately: fake-bank.com/verify. Share your OTP: 123456 to secure account.",
            'sender': "<EMAIL>",
            'communication_type': 'email'
        }

        result = fraud_ai.comprehensive_fraud_detection(phishing_example)

        return jsonify({
            'test_type': 'Phishing Detection',
            'example_used': phishing_example,
            'result': {
                'is_fraud': result.is_fraud,
                'fraud_types': [ft.value for ft in result.specific_fraud_types],
                'risk_score': result.risk_score,
                'evidence': result.evidence,
                'alerts': result.real_time_alerts
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test_upi_fraud', methods=['POST'])
def test_upi_fraud():
    """Test UPI fraud detection with real example"""
    try:
        # Use real UPI fraud example
        upi_example = {
            'analysis_type': 'payment',
            'amount': 75000,
            'merchant': 'Unknown QR Code Scanner',
            'payment_method': 'upi',
            'time': '02:30 AM',
            'location': 'Unknown Location'
        }

        result = fraud_ai.comprehensive_fraud_detection(upi_example)

        return jsonify({
            'test_type': 'UPI Fraud Detection',
            'example_used': upi_example,
            'result': {
                'is_fraud': result.is_fraud,
                'fraud_types': [ft.value for ft in result.specific_fraud_types],
                'risk_score': result.risk_score,
                'evidence': result.evidence,
                'alerts': result.real_time_alerts
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/test_investment_fraud', methods=['POST'])
def test_investment_fraud():
    """Test investment fraud detection with real example"""
    try:
        # Use real investment fraud example
        investment_example = {
            'analysis_type': 'investment',
            'offer': "GUARANTEED 500% returns in just 6 months! No risk, only profit. Join our exclusive investment club. Limited time offer - become millionaire with our secret trading algorithm. 100% success rate guaranteed!",
            'returns_percentage': 500,
            'time_period_months': 6,
            'investment_amount': 100000
        }

        result = fraud_ai.comprehensive_fraud_detection(investment_example)

        return jsonify({
            'test_type': 'Investment Fraud Detection',
            'example_used': investment_example,
            'result': {
                'is_fraud': result.is_fraud,
                'fraud_types': [ft.value for ft in result.specific_fraud_types],
                'risk_score': result.risk_score,
                'evidence': result.evidence,
                'alerts': result.real_time_alerts
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard')
def fraud_detection_dashboard():
    """Advanced fraud detection dashboard with real-time examples"""
    dashboard_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Advanced Real-Time AI Fraud Detection System</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .header { background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); color: white; padding: 20px; text-align: center; }
            .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
            .card { background: white; border-radius: 15px; padding: 25px; margin: 20px 0; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
            .fraud-types-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .fraud-type-card { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; padding: 20px; border-radius: 10px; }
            .test-section { background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 15px 0; }
            .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; margin: 5px; transition: all 0.3s; }
            .btn-primary { background: #667eea; color: white; }
            .btn-danger { background: #ff6b6b; color: white; }
            .btn-success { background: #51cf66; color: white; }
            .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
            .result { margin-top: 20px; padding: 20px; border-radius: 10px; }
            .result.fraud { background: linear-gradient(135deg, #ff6b6b, #ee5a24); color: white; }
            .result.safe { background: linear-gradient(135deg, #51cf66, #40c057); color: white; }
            .result.warning { background: linear-gradient(135deg, #ffd43b, #fab005); color: white; }
            .evidence-list { list-style: none; padding: 0; }
            .evidence-list li { padding: 8px 0; border-bottom: 1px solid rgba(255,255,255,0.2); }
            .real-time-examples { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
            .example-card { background: #fff; border-left: 5px solid #667eea; padding: 20px; border-radius: 8px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
            .stat-card { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center; }
            .stat-number { font-size: 2.5em; font-weight: bold; }
            .loading { text-align: center; padding: 20px; }
            .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #667eea; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto; }
            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ Advanced Real-Time AI Fraud Detection System</h1>
            <p>Comprehensive Detection for All Types of Banking & Financial Frauds</p>
        </div>

        <div class="container">
            <!-- Fraud Types Overview -->
            <div class="card">
                <h2>🎯 Fraud Types Detected by AI System</h2>
                <div class="fraud-types-grid">
                    <div class="fraud-type-card">
                        <h3>📧 Banking & Financial Frauds</h3>
                        <ul>
                            <li>Phishing (Email/SMS)</li>
                            <li>Vishing (Voice Calls)</li>
                            <li>Skimming (Card Cloning)</li>
                            <li>Account Takeover</li>
                            <li>Cheque Frauds</li>
                            <li>Loan Frauds</li>
                        </ul>
                    </div>
                    <div class="fraud-type-card">
                        <h3>💳 Digital Payment Frauds</h3>
                        <ul>
                            <li>UPI Frauds</li>
                            <li>Mobile Wallet Frauds</li>
                            <li>SIM Swap Fraud</li>
                            <li>QR Code Scams</li>
                            <li>Payment Gateway Frauds</li>
                        </ul>
                    </div>
                    <div class="fraud-type-card">
                        <h3>🛒 E-commerce Frauds</h3>
                        <ul>
                            <li>Fake Online Stores</li>
                            <li>Refund Frauds</li>
                            <li>Triangulation Fraud</li>
                            <li>Merchant Impersonation</li>
                        </ul>
                    </div>
                    <div class="fraud-type-card">
                        <h3>💰 Investment Frauds</h3>
                        <ul>
                            <li>Ponzi Schemes</li>
                            <li>Chit Fund Scams</li>
                            <li>Fake IPOs</li>
                            <li>Investment Scams</li>
                        </ul>
                    </div>
                    <div class="fraud-type-card">
                        <h3>🆔 Identity & Cyber Frauds</h3>
                        <ul>
                            <li>Identity Theft</li>
                            <li>Document Forgery</li>
                            <li>Ransomware</li>
                            <li>BEC Frauds</li>
                            <li>Crypto Scams</li>
                        </ul>
                    </div>
                    <div class="fraud-type-card">
                        <h3>🏛️ Government Scheme Frauds</h3>
                        <ul>
                            <li>Subsidy Scams</li>
                            <li>Job Frauds</li>
                            <li>Benefit Frauds</li>
                            <li>Tax Frauds</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Real-Time Testing Section -->
            <div class="card">
                <h2>🧪 Real-Time Fraud Detection Testing</h2>
                <p>Test the AI system with real fraud examples and see immediate results:</p>

                <div class="test-section">
                    <h3>📧 Test Phishing Detection</h3>
                    <p><strong>Example:</strong> "URGENT: Your account will be closed in 24 hours. Click here to verify: fake-bank.com/verify"</p>
                    <button class="btn btn-danger" onclick="testPhishing()">🚨 Test Phishing Detection</button>
                    <div id="phishingResult" class="result" style="display: none;"></div>
                </div>

                <div class="test-section">
                    <h3>💳 Test UPI Fraud Detection</h3>
                    <p><strong>Example:</strong> ₹75,000 payment to "Unknown QR Code Scanner" at 2:30 AM</p>
                    <button class="btn btn-danger" onclick="testUPIFraud()">🚨 Test UPI Fraud Detection</button>
                    <div id="upiResult" class="result" style="display: none;"></div>
                </div>

                <div class="test-section">
                    <h3>💰 Test Investment Fraud Detection</h3>
                    <p><strong>Example:</strong> "GUARANTEED 500% returns in 6 months! No risk, only profit!"</p>
                    <button class="btn btn-danger" onclick="testInvestmentFraud()">🚨 Test Investment Fraud Detection</button>
                    <div id="investmentResult" class="result" style="display: none;"></div>
                </div>

                <div class="test-section">
                    <h3>🔐 Test Account Takeover Detection</h3>
                    <p><strong>Example:</strong> Login from foreign country with new device at 3 AM</p>
                    <button class="btn btn-danger" onclick="testAccountTakeover()">🚨 Test Account Takeover Detection</button>
                    <div id="takeoverResult" class="result" style="display: none;"></div>
                </div>

                <div class="test-section">
                    <h3>📄 Test Identity Theft Detection</h3>
                    <p><strong>Example:</strong> Same Aadhaar number used by multiple accounts</p>
                    <button class="btn btn-danger" onclick="testIdentityTheft()">🚨 Test Identity Theft Detection</button>
                    <div id="identityResult" class="result" style="display: none;"></div>
                </div>

                <div class="test-section">
                    <h3>₿ Test Crypto Scam Detection</h3>
                    <p><strong>Example:</strong> "Guaranteed 1000% crypto returns with our secret algorithm!"</p>
                    <button class="btn btn-danger" onclick="testCryptoScam()">🚨 Test Crypto Scam Detection</button>
                    <div id="cryptoResult" class="result" style="display: none;"></div>
                </div>
            </div>

            <!-- Custom Testing -->
            <div class="card">
                <h2>🔬 Custom Fraud Detection Testing</h2>
                <div class="test-section">
                    <h3>Test Your Own Data</h3>
                    <textarea id="customInput" placeholder="Enter suspicious communication, transaction details, or any fraud-related data..." style="width: 100%; height: 100px; padding: 10px; border-radius: 5px; border: 1px solid #ddd;"></textarea>
                    <br><br>
                    <select id="analysisType" style="padding: 10px; margin-right: 10px;">
                        <option value="comprehensive">Comprehensive Analysis</option>
                        <option value="communication">Communication Analysis</option>
                        <option value="transaction">Transaction Analysis</option>
                        <option value="investment">Investment Analysis</option>
                        <option value="identity">Identity Verification</option>
                    </select>
                    <button class="btn btn-primary" onclick="testCustomData()">🔍 Analyze Custom Data</button>
                    <div id="customResult" class="result" style="display: none;"></div>
                </div>
            </div>
        </div>

        <script>
            function showLoading(elementId) {
                document.getElementById(elementId).style.display = 'block';
                document.getElementById(elementId).innerHTML = '<div class="loading"><div class="spinner"></div><p>AI analyzing for fraud patterns...</p></div>';
            }

            function showResult(elementId, result) {
                const element = document.getElementById(elementId);
                element.style.display = 'block';

                let className = 'safe';
                let icon = '✅';
                let title = 'LEGITIMATE';

                if (result.is_fraud) {
                    className = 'fraud';
                    icon = '🚨';
                    title = 'FRAUD DETECTED';
                } else if (result.risk_score > 30) {
                    className = 'warning';
                    icon = '⚠️';
                    title = 'SUSPICIOUS';
                }

                element.className = `result ${className}`;
                element.innerHTML = `
                    <h3>${icon} ${title}</h3>
                    <p><strong>Risk Score:</strong> ${result.risk_score.toFixed(1)}%</p>
                    <p><strong>Confidence:</strong> ${(result.confidence_score * 100).toFixed(1)}%</p>
                    <p><strong>Fraud Types:</strong> ${result.fraud_types.join(', ') || 'None'}</p>
                    <p><strong>Evidence Found:</strong></p>
                    <ul class="evidence-list">
                        ${result.evidence.map(e => '<li>' + e + '</li>').join('')}
                    </ul>
                    ${result.alerts ? '<p><strong>Real-Time Alerts:</strong></p><ul class="evidence-list">' + result.alerts.map(a => '<li>' + a + '</li>').join('') + '</ul>' : ''}
                `;
            }

            async function testPhishing() {
                showLoading('phishingResult');
                try {
                    const response = await fetch('/api/test_phishing', { method: 'POST' });
                    const data = await response.json();
                    showResult('phishingResult', data.result);
                } catch (error) {
                    document.getElementById('phishingResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }

            async function testUPIFraud() {
                showLoading('upiResult');
                try {
                    const response = await fetch('/api/test_upi_fraud', { method: 'POST' });
                    const data = await response.json();
                    showResult('upiResult', data.result);
                } catch (error) {
                    document.getElementById('upiResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }

            async function testInvestmentFraud() {
                showLoading('investmentResult');
                try {
                    const response = await fetch('/api/test_investment_fraud', { method: 'POST' });
                    const data = await response.json();
                    showResult('investmentResult', data.result);
                } catch (error) {
                    document.getElementById('investmentResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }

            async function testAccountTakeover() {
                showLoading('takeoverResult');
                try {
                    const testData = {
                        analysis_type: 'security',
                        location: 'Foreign Country',
                        device: 'New Unknown Device',
                        time: '03:00 AM',
                        failed_attempts: 5,
                        previous_location: 'India'
                    };

                    const response = await fetch('/api/detect_fraud', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });
                    const data = await response.json();
                    showResult('takeoverResult', data);
                } catch (error) {
                    document.getElementById('takeoverResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }

            async function testIdentityTheft() {
                showLoading('identityResult');
                try {
                    const testData = {
                        analysis_type: 'identity',
                        document_type: 'aadhaar',
                        document_number: '123456789012',
                        user_data: {
                            name: 'Test User',
                            email: 'invalid-email',
                            phone: 'invalid-phone'
                        }
                    };

                    const response = await fetch('/api/detect_fraud', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });
                    const data = await response.json();
                    showResult('identityResult', data);
                } catch (error) {
                    document.getElementById('identityResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }

            async function testCryptoScam() {
                showLoading('cryptoResult');
                try {
                    const testData = {
                        analysis_type: 'crypto',
                        platform_name: 'QuickCrypto Guaranteed Returns',
                        promised_returns: 1000,
                        investment_amount: 50000
                    };

                    const response = await fetch('/api/detect_fraud', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });
                    const data = await response.json();
                    showResult('cryptoResult', data);
                } catch (error) {
                    document.getElementById('cryptoResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }

            async function testCustomData() {
                const customInput = document.getElementById('customInput').value;
                const analysisType = document.getElementById('analysisType').value;

                if (!customInput.trim()) {
                    alert('Please enter some data to analyze');
                    return;
                }

                showLoading('customResult');
                try {
                    const testData = {
                        analysis_type: analysisType,
                        content: customInput,
                        offer: customInput,
                        amount: 10000,
                        merchant: customInput
                    };

                    const response = await fetch('/api/detect_fraud', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });
                    const data = await response.json();
                    showResult('customResult', data);
                } catch (error) {
                    document.getElementById('customResult').innerHTML = '<p>Error: ' + error.message + '</p>';
                }
            }
        </script>
    </body>
    </html>
    """
    return dashboard_html

if __name__ == '__main__':
    print("🚀 Starting Advanced Real-Time AI Fraud Detection System...")
    print("🎯 Dashboard: http://localhost:5003/api/dashboard")
    print("🔍 API Endpoints:")
    print("   - POST /api/detect_fraud - Comprehensive fraud detection")
    print("   - POST /api/test_phishing - Test phishing detection")
    print("   - POST /api/test_upi_fraud - Test UPI fraud detection")
    print("   - POST /api/test_investment_fraud - Test investment fraud detection")
    print("   - GET  /api/test_examples - Get all test examples")
    print("")
    print("🛡️ Fraud Types Detected:")
    print("   ✅ Phishing & Vishing")
    print("   ✅ UPI & Digital Payment Frauds")
    print("   ✅ Account Takeover")
    print("   ✅ Investment & Ponzi Schemes")
    print("   ✅ Identity Theft & Document Fraud")
    print("   ✅ Cryptocurrency Scams")
    print("   ✅ E-commerce Frauds")
    print("   ✅ Government Scheme Frauds")
    print("")
    print("🤖 AI Technologies Used:")
    print("   • Random Forest Classifier")
    print("   • Isolation Forest (Anomaly Detection)")
    print("   • Gradient Boosting")
    print("   • Neural Networks (MLP)")
    print("   • TF-IDF Text Vectorization")
    print("   • Naive Bayes")
    print("   • SVM Classification")
    print("")
    app.run(debug=True, host='0.0.0.0', port=5003)
