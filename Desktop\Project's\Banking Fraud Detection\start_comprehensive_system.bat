@echo off
echo ========================================
echo   Comprehensive Fraud Detection System
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo Python found! Installing comprehensive dependencies...
pip install -r comprehensive_requirements.txt

echo.
echo Starting comprehensive fraud detection system...
echo.
echo ========================================
echo   Access Points:
echo   Fraud Dashboard: http://localhost:5001/api/fraud_dashboard
echo   API Documentation: http://localhost:5001/api/fraud_statistics
echo ========================================
echo.
echo This system can detect:
echo   ✓ Phishing and Vishing attacks
echo   ✓ Account takeover attempts
echo   ✓ UPI and digital payment frauds
echo   ✓ Investment and Ponzi schemes
echo   ✓ Identity theft
echo   ✓ SIM swap fraud
echo   ✓ E-commerce frauds
echo   ✓ Insurance frauds
echo   ✓ Cryptocurrency scams
echo   ✓ Government scheme frauds
echo.
echo Press Ctrl+C to stop the server
echo.

python comprehensive_fraud_detection.py
