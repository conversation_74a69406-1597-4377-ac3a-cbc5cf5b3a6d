#!/usr/bin/env python3
"""
Comprehensive AI-Powered Fraud Detection System
Detects multiple types of banking and financial frauds using advanced ML techniques
"""

import json
import datetime
import sqlite3
import numpy as np
import pandas as pd
import re
import hashlib
import requests
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# ML Libraries
from sklearn.ensemble import RandomForestClassifier, IsolationForest, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import DBSCAN
from sklearn.model_selection import train_test_split
import joblib

# Flask and API
from flask import Flask, request, jsonify
from flask_cors import CORS
import threading
import time

app = Flask(__name__)
CORS(app)

class FraudType(Enum):
    PHISHING = "phishing"
    VISHING = "vishing"
    SKIMMING = "skimming"
    ACCOUNT_TAKEOVER = "account_takeover"
    CHEQUE_FRAUD = "cheque_fraud"
    LOAN_FRAUD = "loan_fraud"
    UPI_FRAUD = "upi_fraud"
    WALLET_FRAUD = "wallet_fraud"
    SIM_SWAP = "sim_swap"
    ECOMMERCE_FRAUD = "ecommerce_fraud"
    REFUND_FRAUD = "refund_fraud"
    TRIANGULATION_FRAUD = "triangulation_fraud"
    PONZI_SCHEME = "ponzi_scheme"
    INVESTMENT_FRAUD = "investment_fraud"
    INSURANCE_FRAUD = "insurance_fraud"
    IDENTITY_THEFT = "identity_theft"
    RANSOMWARE = "ransomware"
    BEC_FRAUD = "bec_fraud"
    CRYPTO_SCAM = "crypto_scam"
    SUBSIDY_FRAUD = "subsidy_fraud"
    JOB_FRAUD = "job_fraud"

@dataclass
class FraudDetectionResult:
    is_fraud: bool
    fraud_types: List[FraudType]
    confidence_score: float
    risk_score: float
    reasons: List[str]
    recommended_actions: List[str]

class ComprehensiveFraudDetector:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.vectorizers = {}
        self.setup_database()
        self.initialize_models()
        self.load_fraud_patterns()

    def setup_database(self):
        """Setup comprehensive database schema"""
        self.conn = sqlite3.connect('comprehensive_fraud.db', check_same_thread=False)
        cursor = self.conn.cursor()

        # Enhanced transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id TEXT UNIQUE NOT NULL,
                user_id TEXT NOT NULL,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                merchant TEXT,
                merchant_category TEXT,
                location TEXT,
                ip_address TEXT,
                device_fingerprint TEXT,
                user_agent TEXT,
                payment_method TEXT,
                currency TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_fraud INTEGER DEFAULT 0,
                fraud_types TEXT,
                risk_score REAL DEFAULT 0,
                confidence_score REAL DEFAULT 0,
                features TEXT
            )
        ''')

        # User behavior patterns
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                avg_transaction_amount REAL,
                typical_locations TEXT,
                usual_merchants TEXT,
                common_transaction_times TEXT,
                device_patterns TEXT,
                spending_velocity REAL,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Communication logs (for phishing/vishing detection)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS communications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                communication_type TEXT,
                sender_info TEXT,
                content TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_suspicious INTEGER DEFAULT 0,
                fraud_indicators TEXT
            )
        ''')

        # Device and session tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS device_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                device_id TEXT,
                ip_address TEXT,
                location TEXT,
                user_agent TEXT,
                session_start DATETIME,
                session_end DATETIME,
                is_suspicious INTEGER DEFAULT 0
            )
        ''')

        # Known fraud patterns
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fraud_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_type TEXT NOT NULL,
                pattern_data TEXT NOT NULL,
                confidence REAL DEFAULT 0.8,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        self.conn.commit()

    def initialize_models(self):
        """Initialize multiple ML models for different fraud types"""

        # Transaction fraud model (Random Forest)
        self.models['transaction'] = RandomForestClassifier(
            n_estimators=100, random_state=42, max_depth=10
        )

        # Anomaly detection model (Isolation Forest)
        self.models['anomaly'] = IsolationForest(
            contamination=0.1, random_state=42
        )

        # Text analysis model for phishing detection
        self.vectorizers['text'] = TfidfVectorizer(
            max_features=1000, stop_words='english'
        )
        self.models['phishing'] = GradientBoostingClassifier(
            n_estimators=100, random_state=42
        )

        # Neural network for complex pattern detection
        self.models['neural'] = MLPClassifier(
            hidden_layer_sizes=(100, 50), random_state=42, max_iter=500
        )

        # Clustering for behavioral analysis
        self.models['clustering'] = DBSCAN(eps=0.5, min_samples=5)

        # Scalers for different feature sets
        self.scalers['transaction'] = StandardScaler()
        self.scalers['behavioral'] = StandardScaler()

        # Train models with synthetic data
        self.train_models()

    def train_models(self):
        """Train all models with synthetic fraud data"""
        # Generate synthetic training data
        transaction_data = self.generate_transaction_training_data()
        phishing_data = self.generate_phishing_training_data()

        # Train transaction fraud model
        X_trans, y_trans = transaction_data
        X_trans_scaled = self.scalers['transaction'].fit_transform(X_trans)
        self.models['transaction'].fit(X_trans_scaled, y_trans)
        self.models['anomaly'].fit(X_trans_scaled[y_trans == 0])  # Train on normal data

        # Train phishing detection model
        X_phish, y_phish = phishing_data
        X_phish_vec = self.vectorizers['text'].fit_transform(X_phish)
        self.models['phishing'].fit(X_phish_vec, y_phish)

        print("All fraud detection models trained successfully!")

    def generate_transaction_training_data(self):
        """Generate synthetic transaction data for training"""
        np.random.seed(42)
        n_samples = 10000

        # Features: amount, hour, day_of_week, merchant_risk, location_risk,
        # velocity, device_change, unusual_pattern
        features = []
        labels = []

        # Normal transactions (80%)
        for _ in range(int(n_samples * 0.8)):
            amount = np.random.lognormal(4, 1)  # Normal spending
            hour = np.random.normal(14, 4)  # Peak hours
            hour = max(0, min(23, hour))
            day_of_week = np.random.randint(0, 7)
            merchant_risk = np.random.normal(0.2, 0.1)
            location_risk = np.random.normal(0.1, 0.05)
            velocity = np.random.normal(0.3, 0.1)
            device_change = 0
            unusual_pattern = 0

            features.append([amount, hour, day_of_week, merchant_risk,
                           location_risk, velocity, device_change, unusual_pattern])
            labels.append(0)

        # Fraudulent transactions (20%)
        for _ in range(int(n_samples * 0.2)):
            amount = np.random.lognormal(6, 1.5)  # Higher amounts
            hour = np.random.choice([2, 3, 4, 23, 0, 1])  # Unusual hours
            day_of_week = np.random.randint(0, 7)
            merchant_risk = np.random.normal(0.8, 0.2)
            location_risk = np.random.normal(0.9, 0.1)
            velocity = np.random.normal(0.9, 0.1)
            device_change = np.random.choice([0, 1], p=[0.3, 0.7])
            unusual_pattern = 1

            features.append([amount, hour, day_of_week, merchant_risk,
                           location_risk, velocity, device_change, unusual_pattern])
            labels.append(1)

        return np.array(features), np.array(labels)

    def generate_phishing_training_data(self):
        """Generate synthetic phishing text data"""
        # Legitimate messages
        legitimate = [
            "Your account balance is $1000. Thank you for banking with us.",
            "Transaction successful. Amount: $50.00 at Store ABC.",
            "Monthly statement is ready for download.",
            "Your loan payment is due on 15th of this month.",
            "Welcome to our mobile banking app.",
            "ATM withdrawal of $100 successful.",
            "Direct deposit received in your account.",
            "Your card ending in 1234 was used for purchase."
        ]

        # Phishing messages
        phishing = [
            "URGENT: Your account will be closed. Click here to verify immediately.",
            "Congratulations! You've won $10000. Claim now with your bank details.",
            "Security alert: Suspicious activity detected. Share OTP to secure account.",
            "Your card is blocked. Call this number and provide PIN to unblock.",
            "Bank verification required. Enter username and password on this link.",
            "Immediate action needed. Your account compromised. Share details.",
            "Limited time offer: Get loan instantly. Provide Aadhaar and PAN.",
            "Your KYC is expired. Update now or account will be frozen."
        ]

        # Create training data
        texts = legitimate * 100 + phishing * 100
        labels = [0] * 800 + [1] * 800

        return texts, labels

    def load_fraud_patterns(self):
        """Load known fraud patterns from database"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT pattern_type, pattern_data FROM fraud_patterns')

        self.fraud_patterns = {}
        for row in cursor.fetchall():
            pattern_type, pattern_data = row
            if pattern_type not in self.fraud_patterns:
                self.fraud_patterns[pattern_type] = []
            self.fraud_patterns[pattern_type].append(json.loads(pattern_data))

    def extract_transaction_features(self, transaction: Dict) -> np.array:
        """Extract features from transaction for ML models"""
        timestamp = datetime.datetime.fromisoformat(
            transaction.get('timestamp', datetime.datetime.now().isoformat())
        )

        # Basic features
        amount = float(transaction.get('amount', 0))
        hour = timestamp.hour
        day_of_week = timestamp.weekday()

        # Risk scores
        merchant_risk = self.calculate_merchant_risk(transaction.get('merchant', ''))
        location_risk = self.calculate_location_risk(transaction.get('location', ''))
        velocity_risk = self.calculate_velocity_risk(transaction.get('user_id', ''))
        device_risk = self.calculate_device_risk(transaction)

        # Pattern analysis
        unusual_pattern = self.detect_unusual_patterns(transaction)

        features = [
            amount, hour, day_of_week, merchant_risk, location_risk,
            velocity_risk, device_risk, unusual_pattern
        ]

        return np.array(features).reshape(1, -1)

    def calculate_merchant_risk(self, merchant: str) -> float:
        """Calculate risk score for merchant"""
        high_risk_keywords = ['crypto', 'casino', 'gambling', 'forex', 'investment']
        risk_score = 0.1

        for keyword in high_risk_keywords:
            if keyword.lower() in merchant.lower():
                risk_score += 0.3

        return min(risk_score, 1.0)

    def calculate_location_risk(self, location: str) -> float:
        """Calculate risk score for transaction location"""
        # Simulate location risk based on known patterns
        high_risk_locations = ['unknown', 'foreign', 'vpn', 'proxy']
        risk_score = 0.1

        for loc in high_risk_locations:
            if loc.lower() in location.lower():
                risk_score += 0.4

        return min(risk_score, 1.0)

    def calculate_velocity_risk(self, user_id: str) -> float:
        """Calculate transaction velocity risk"""
        cursor = self.conn.cursor()

        # Check transactions in last hour
        one_hour_ago = datetime.datetime.now() - datetime.timedelta(hours=1)
        cursor.execute('''
            SELECT COUNT(*) FROM transactions
            WHERE user_id = ? AND timestamp > ?
        ''', (user_id, one_hour_ago))

        recent_count = cursor.fetchone()[0]

        # Risk increases with transaction frequency
        if recent_count > 10:
            return 0.9
        elif recent_count > 5:
            return 0.6
        elif recent_count > 2:
            return 0.3
        else:
            return 0.1

    def calculate_device_risk(self, transaction: Dict) -> float:
        """Calculate device-based risk score"""
        user_id = transaction.get('user_id', '')
        device_fingerprint = transaction.get('device_fingerprint', '')
        ip_address = transaction.get('ip_address', '')

        cursor = self.conn.cursor()

        # Check if device is new for this user
        cursor.execute('''
            SELECT COUNT(*) FROM device_sessions
            WHERE user_id = ? AND device_id = ?
        ''', (user_id, device_fingerprint))

        device_known = cursor.fetchone()[0] > 0

        if not device_known:
            return 0.7  # High risk for new device
        else:
            return 0.1  # Low risk for known device

    def detect_unusual_patterns(self, transaction: Dict) -> int:
        """Detect unusual transaction patterns"""
        user_id = transaction.get('user_id', '')
        amount = float(transaction.get('amount', 0))

        cursor = self.conn.cursor()

        # Get user's average transaction amount
        cursor.execute('''
            SELECT AVG(amount) FROM transactions
            WHERE user_id = ? AND timestamp > datetime('now', '-30 days')
        ''', (user_id,))

        result = cursor.fetchone()[0]
        avg_amount = result if result else 100

        # Check if amount is significantly higher than usual
        if amount > avg_amount * 5:
            return 1
        else:
            return 0

    def detect_phishing_fraud(self, communication_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect phishing attempts in communications"""
        content = communication_data.get('content', '').lower()
        sender = communication_data.get('sender_info', '').lower()

        phishing_indicators = []
        risk_score = 0.0

        # Phishing keywords and patterns
        urgent_keywords = ['urgent', 'immediate', 'expire', 'suspend', 'block', 'freeze']
        suspicious_requests = ['otp', 'pin', 'password', 'cvv', 'card number', 'account number']
        fake_rewards = ['congratulations', 'winner', 'prize', 'lottery', 'reward']

        # Check for urgent language
        for keyword in urgent_keywords:
            if keyword in content:
                risk_score += 0.2
                phishing_indicators.append(f"Urgent language detected: {keyword}")

        # Check for credential requests
        for request in suspicious_requests:
            if request in content:
                risk_score += 0.3
                phishing_indicators.append(f"Credential request detected: {request}")

        # Check for fake rewards
        for reward in fake_rewards:
            if reward in content:
                risk_score += 0.25
                phishing_indicators.append(f"Fake reward indicator: {reward}")

        # Check sender authenticity
        if 'bank' in sender and not self.verify_sender_authenticity(sender):
            risk_score += 0.4
            phishing_indicators.append("Suspicious sender impersonating bank")

        # Use ML model for text classification
        if hasattr(self.vectorizers['text'], 'vocabulary_'):
            text_vector = self.vectorizers['text'].transform([content])
            ml_prediction = self.models['phishing'].predict_proba(text_vector)[0][1]
            risk_score += ml_prediction * 0.5

        is_phishing = risk_score > 0.6
        return is_phishing, min(risk_score, 1.0), phishing_indicators

    def detect_account_takeover(self, session_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect account takeover attempts"""
        user_id = session_data.get('user_id', '')
        ip_address = session_data.get('ip_address', '')
        device_fingerprint = session_data.get('device_fingerprint', '')
        location = session_data.get('location', '')

        takeover_indicators = []
        risk_score = 0.0

        cursor = self.conn.cursor()

        # Check for multiple failed login attempts
        cursor.execute('''
            SELECT COUNT(*) FROM device_sessions
            WHERE user_id = ? AND timestamp > datetime('now', '-1 hour')
            AND is_suspicious = 1
        ''', (user_id,))

        failed_attempts = cursor.fetchone()[0]
        if failed_attempts > 3:
            risk_score += 0.4
            takeover_indicators.append(f"Multiple failed login attempts: {failed_attempts}")

        # Check for new device/location combination
        cursor.execute('''
            SELECT COUNT(*) FROM device_sessions
            WHERE user_id = ? AND device_id = ? AND location = ?
        ''', (user_id, device_fingerprint, location))

        known_combination = cursor.fetchone()[0] > 0
        if not known_combination:
            risk_score += 0.3
            takeover_indicators.append("New device and location combination")

        # Check for impossible travel (location changes too quickly)
        cursor.execute('''
            SELECT location, timestamp FROM device_sessions
            WHERE user_id = ? ORDER BY timestamp DESC LIMIT 2
        ''', (user_id,))

        recent_locations = cursor.fetchall()
        if len(recent_locations) == 2:
            if self.detect_impossible_travel(recent_locations[0], recent_locations[1]):
                risk_score += 0.5
                takeover_indicators.append("Impossible travel detected")

        is_takeover = risk_score > 0.6
        return is_takeover, min(risk_score, 1.0), takeover_indicators

    def detect_sim_swap_fraud(self, transaction_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect SIM swap fraud indicators"""
        user_id = transaction_data.get('user_id', '')
        phone_number = transaction_data.get('phone_number', '')

        sim_swap_indicators = []
        risk_score = 0.0

        cursor = self.conn.cursor()

        # Check for recent mobile number changes
        cursor.execute('''
            SELECT COUNT(*) FROM account_activity
            WHERE user_id = ? AND activity_type = 'phone_change'
            AND timestamp > datetime('now', '-7 days')
        ''', (user_id,))

        recent_phone_changes = cursor.fetchone()[0]
        if recent_phone_changes > 0:
            risk_score += 0.6
            sim_swap_indicators.append("Recent phone number change detected")

        # Check for OTP bypass attempts
        cursor.execute('''
            SELECT COUNT(*) FROM communications
            WHERE user_id = ? AND communication_type = 'otp_request'
            AND timestamp > datetime('now', '-1 hour')
        ''', (user_id,))

        otp_requests = cursor.fetchone()[0]
        if otp_requests > 5:
            risk_score += 0.4
            sim_swap_indicators.append(f"Excessive OTP requests: {otp_requests}")

        is_sim_swap = risk_score > 0.7
        return is_sim_swap, min(risk_score, 1.0), sim_swap_indicators

    def detect_upi_fraud(self, transaction_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect UPI-specific fraud patterns"""
        payment_method = transaction_data.get('payment_method', '').lower()
        merchant = transaction_data.get('merchant', '').lower()
        amount = float(transaction_data.get('amount', 0))

        upi_indicators = []
        risk_score = 0.0

        if 'upi' not in payment_method:
            return False, 0.0, []

        # Check for fake QR code patterns
        if 'qr' in transaction_data.get('description', '').lower():
            if self.is_suspicious_qr_merchant(merchant):
                risk_score += 0.5
                upi_indicators.append("Suspicious QR code merchant")

        # Check for round amounts (common in fraud)
        if amount % 100 == 0 and amount > 1000:
            risk_score += 0.2
            upi_indicators.append("Suspicious round amount")

        # Check for merchant category mismatch
        if self.detect_merchant_category_mismatch(transaction_data):
            risk_score += 0.3
            upi_indicators.append("Merchant category mismatch")

        is_upi_fraud = risk_score > 0.5
        return is_upi_fraud, min(risk_score, 1.0), upi_indicators

    def detect_investment_fraud(self, transaction_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect investment and Ponzi scheme fraud"""
        merchant = transaction_data.get('merchant', '').lower()
        amount = float(transaction_data.get('amount', 0))
        description = transaction_data.get('description', '').lower()

        investment_indicators = []
        risk_score = 0.0

        # Check for investment-related keywords
        investment_keywords = ['investment', 'trading', 'forex', 'crypto', 'bitcoin',
                             'scheme', 'returns', 'profit', 'guaranteed']

        for keyword in investment_keywords:
            if keyword in merchant or keyword in description:
                risk_score += 0.2
                investment_indicators.append(f"Investment keyword detected: {keyword}")

        # Check for high-return promises
        high_return_phrases = ['guaranteed returns', 'double money', 'high profit',
                              'risk-free', 'assured returns']

        for phrase in high_return_phrases:
            if phrase in description:
                risk_score += 0.4
                investment_indicators.append(f"High-return promise: {phrase}")

        # Check for unusual payment patterns (typical in Ponzi schemes)
        if self.detect_ponzi_payment_pattern(transaction_data):
            risk_score += 0.5
            investment_indicators.append("Ponzi scheme payment pattern detected")

        is_investment_fraud = risk_score > 0.6
        return is_investment_fraud, min(risk_score, 1.0), investment_indicators

    def detect_identity_theft(self, user_data: Dict) -> Tuple[bool, float, List[str]]:
        """Detect identity theft indicators"""
        user_id = user_data.get('user_id', '')
        provided_details = user_data.get('identity_details', {})

        identity_indicators = []
        risk_score = 0.0

        # Check for multiple accounts with same identity documents
        cursor = self.conn.cursor()

        aadhaar = provided_details.get('aadhaar', '')
        pan = provided_details.get('pan', '')

        if aadhaar:
            cursor.execute('''
                SELECT COUNT(DISTINCT user_id) FROM user_identity
                WHERE aadhaar = ? AND user_id != ?
            ''', (aadhaar, user_id))

            aadhaar_users = cursor.fetchone()[0]
            if aadhaar_users > 0:
                risk_score += 0.7
                identity_indicators.append(f"Aadhaar used by {aadhaar_users} other accounts")

        if pan:
            cursor.execute('''
                SELECT COUNT(DISTINCT user_id) FROM user_identity
                WHERE pan = ? AND user_id != ?
            ''', (pan, user_id))

            pan_users = cursor.fetchone()[0]
            if pan_users > 0:
                risk_score += 0.7
                identity_indicators.append(f"PAN used by {pan_users} other accounts")

        # Check for document format validation
        if not self.validate_identity_documents(provided_details):
            risk_score += 0.4
            identity_indicators.append("Invalid identity document format")

        is_identity_theft = risk_score > 0.6
        return is_identity_theft, min(risk_score, 1.0), identity_indicators

    # Helper methods
    def verify_sender_authenticity(self, sender: str) -> bool:
        """Verify if sender is authentic bank communication"""
        authentic_domains = ['@securebank.com', '@bank.com', '@hdfc.com', '@sbi.com']
        return any(domain in sender for domain in authentic_domains)

    def detect_impossible_travel(self, location1: Tuple, location2: Tuple) -> bool:
        """Detect if travel between locations is impossible in given time"""
        # Simplified implementation - in real scenario, use geolocation APIs
        loc1, time1 = location1
        loc2, time2 = location2

        if loc1 == loc2:
            return False

        # Calculate time difference
        time_diff = abs((datetime.datetime.fromisoformat(time1) -
                        datetime.datetime.fromisoformat(time2)).total_seconds())

        # If locations are different and time difference is less than 1 hour
        if time_diff < 3600:  # 1 hour
            return True

        return False

    def is_suspicious_qr_merchant(self, merchant: str) -> bool:
        """Check if QR code merchant is suspicious"""
        suspicious_patterns = ['unknown', 'temp', 'test', 'fake', 'scam']
        return any(pattern in merchant.lower() for pattern in suspicious_patterns)

    def detect_merchant_category_mismatch(self, transaction_data: Dict) -> bool:
        """Detect if merchant category doesn't match transaction type"""
        merchant = transaction_data.get('merchant', '').lower()
        merchant_category = transaction_data.get('merchant_category', '').lower()
        amount = float(transaction_data.get('amount', 0))

        # Example: High amount at grocery store might be suspicious
        if 'grocery' in merchant_category and amount > 5000:
            return True

        # Medical merchant with electronics category
        if 'medical' in merchant and 'electronics' in merchant_category:
            return True

        return False

    def detect_ponzi_payment_pattern(self, transaction_data: Dict) -> bool:
        """Detect Ponzi scheme payment patterns"""
        user_id = transaction_data.get('user_id', '')
        amount = float(transaction_data.get('amount', 0))

        cursor = self.conn.cursor()

        # Check for regular payments to same merchant (typical in Ponzi schemes)
        cursor.execute('''
            SELECT COUNT(*), AVG(amount) FROM transactions
            WHERE user_id = ? AND merchant = ?
            AND timestamp > datetime('now', '-30 days')
        ''', (user_id, transaction_data.get('merchant', '')))

        result = cursor.fetchone()
        count, avg_amount = result if result[0] else (0, 0)

        # Regular payments of similar amounts might indicate Ponzi scheme
        if count > 5 and abs(amount - avg_amount) < avg_amount * 0.1:
            return True

        return False

    def validate_identity_documents(self, documents: Dict) -> bool:
        """Validate format of identity documents"""
        aadhaar = documents.get('aadhaar', '')
        pan = documents.get('pan', '')

        # Aadhaar validation (12 digits)
        if aadhaar and not re.match(r'^\d{12}$', aadhaar):
            return False

        # PAN validation (5 letters, 4 digits, 1 letter)
        if pan and not re.match(r'^[A-Z]{5}\d{4}[A-Z]$', pan):
            return False

        return True

    def comprehensive_fraud_analysis(self, data: Dict) -> FraudDetectionResult:
        """Main method to analyze all types of fraud"""
        detected_frauds = []
        all_indicators = []
        max_confidence = 0.0
        max_risk = 0.0

        data_type = data.get('analysis_type', 'transaction')

        # Transaction-based fraud detection
        if data_type in ['transaction', 'all']:
            # Basic transaction fraud
            features = self.extract_transaction_features(data)
            features_scaled = self.scalers['transaction'].transform(features)

            # ML model prediction
            fraud_prob = self.models['transaction'].predict_proba(features_scaled)[0][1]
            anomaly_score = self.models['anomaly'].decision_function(features_scaled)[0]

            if fraud_prob > 0.6 or anomaly_score < -0.1:
                detected_frauds.append(FraudType.ACCOUNT_TAKEOVER)
                max_confidence = max(max_confidence, fraud_prob)
                max_risk = max(max_risk, fraud_prob * 100)
                all_indicators.append(f"ML model fraud probability: {fraud_prob:.2f}")

            # UPI fraud detection
            is_upi_fraud, upi_risk, upi_indicators = self.detect_upi_fraud(data)
            if is_upi_fraud:
                detected_frauds.append(FraudType.UPI_FRAUD)
                max_confidence = max(max_confidence, upi_risk)
                max_risk = max(max_risk, upi_risk * 100)
                all_indicators.extend(upi_indicators)

            # Investment fraud detection
            is_investment_fraud, inv_risk, inv_indicators = self.detect_investment_fraud(data)
            if is_investment_fraud:
                detected_frauds.append(FraudType.INVESTMENT_FRAUD)
                max_confidence = max(max_confidence, inv_risk)
                max_risk = max(max_risk, inv_risk * 100)
                all_indicators.extend(inv_indicators)

            # SIM swap detection
            is_sim_swap, sim_risk, sim_indicators = self.detect_sim_swap_fraud(data)
            if is_sim_swap:
                detected_frauds.append(FraudType.SIM_SWAP)
                max_confidence = max(max_confidence, sim_risk)
                max_risk = max(max_risk, sim_risk * 100)
                all_indicators.extend(sim_indicators)

        # Communication-based fraud detection
        if data_type in ['communication', 'all']:
            is_phishing, phish_risk, phish_indicators = self.detect_phishing_fraud(data)
            if is_phishing:
                detected_frauds.append(FraudType.PHISHING)
                max_confidence = max(max_confidence, phish_risk)
                max_risk = max(max_risk, phish_risk * 100)
                all_indicators.extend(phish_indicators)

        # Session-based fraud detection
        if data_type in ['session', 'all']:
            is_takeover, takeover_risk, takeover_indicators = self.detect_account_takeover(data)
            if is_takeover:
                detected_frauds.append(FraudType.ACCOUNT_TAKEOVER)
                max_confidence = max(max_confidence, takeover_risk)
                max_risk = max(max_risk, takeover_risk * 100)
                all_indicators.extend(takeover_indicators)

        # Identity verification
        if data_type in ['identity', 'all']:
            is_identity_theft, id_risk, id_indicators = self.detect_identity_theft(data)
            if is_identity_theft:
                detected_frauds.append(FraudType.IDENTITY_THEFT)
                max_confidence = max(max_confidence, id_risk)
                max_risk = max(max_risk, id_risk * 100)
                all_indicators.extend(id_indicators)

        # Generate recommendations
        recommendations = self.generate_recommendations(detected_frauds, max_risk)

        return FraudDetectionResult(
            is_fraud=len(detected_frauds) > 0,
            fraud_types=detected_frauds,
            confidence_score=max_confidence,
            risk_score=max_risk,
            reasons=all_indicators,
            recommended_actions=recommendations
        )

    def generate_recommendations(self, fraud_types: List[FraudType], risk_score: float) -> List[str]:
        """Generate recommended actions based on detected fraud types"""
        recommendations = []

        if risk_score > 80:
            recommendations.append("IMMEDIATE ACTION: Block transaction and freeze account")
            recommendations.append("Contact customer immediately via verified phone number")
            recommendations.append("Escalate to fraud investigation team")
        elif risk_score > 60:
            recommendations.append("Hold transaction for manual review")
            recommendations.append("Send security alert to customer")
            recommendations.append("Require additional authentication")
        elif risk_score > 40:
            recommendations.append("Monitor account for suspicious activity")
            recommendations.append("Log incident for pattern analysis")

        # Specific recommendations for fraud types
        if FraudType.PHISHING in fraud_types:
            recommendations.append("Block sender and report phishing attempt")
            recommendations.append("Educate customer about phishing awareness")

        if FraudType.ACCOUNT_TAKEOVER in fraud_types:
            recommendations.append("Force password reset and logout all sessions")
            recommendations.append("Enable two-factor authentication")

        if FraudType.SIM_SWAP in fraud_types:
            recommendations.append("Verify phone number change with telecom provider")
            recommendations.append("Temporarily disable SMS-based authentication")

        if FraudType.IDENTITY_THEFT in fraud_types:
            recommendations.append("Request additional identity verification documents")
            recommendations.append("Cross-check with government databases")

        return recommendations

# Initialize the comprehensive fraud detector
fraud_detector = ComprehensiveFraudDetector()

# Flask API Endpoints

@app.route('/api/analyze_comprehensive_fraud', methods=['POST'])
def analyze_comprehensive_fraud():
    """Comprehensive fraud analysis endpoint"""
    try:
        data = request.json

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Perform comprehensive fraud analysis
        result = fraud_detector.comprehensive_fraud_analysis(data)

        # Convert enum values to strings for JSON serialization
        fraud_types_str = [fraud_type.value for fraud_type in result.fraud_types]

        response = {
            'is_fraud': result.is_fraud,
            'fraud_types': fraud_types_str,
            'confidence_score': result.confidence_score,
            'risk_score': result.risk_score,
            'reasons': result.reasons,
            'recommended_actions': result.recommended_actions,
            'analysis_timestamp': datetime.datetime.now().isoformat()
        }

        # Store analysis result in database
        cursor = fraud_detector.conn.cursor()
        cursor.execute('''
            INSERT INTO transactions (
                transaction_id, user_id, transaction_type, amount, merchant,
                is_fraud, fraud_types, risk_score, confidence_score, features
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('transaction_id', 'ANALYSIS_' + str(int(time.time()))),
            data.get('user_id', 'unknown'),
            data.get('analysis_type', 'comprehensive'),
            data.get('amount', 0),
            data.get('merchant', ''),
            1 if result.is_fraud else 0,
            json.dumps(fraud_types_str),
            result.risk_score,
            result.confidence_score,
            json.dumps(data)
        ))
        fraud_detector.conn.commit()

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze_phishing', methods=['POST'])
def analyze_phishing():
    """Analyze communication for phishing attempts"""
    try:
        data = request.json

        if not data or 'content' not in data:
            return jsonify({'error': 'Communication content required'}), 400

        is_phishing, risk_score, indicators = fraud_detector.detect_phishing_fraud(data)

        return jsonify({
            'is_phishing': is_phishing,
            'risk_score': risk_score * 100,
            'indicators': indicators,
            'recommendation': 'Block sender and alert user' if is_phishing else 'Communication appears legitimate'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze_account_takeover', methods=['POST'])
def analyze_account_takeover():
    """Analyze session for account takeover attempts"""
    try:
        data = request.json

        if not data or 'user_id' not in data:
            return jsonify({'error': 'User ID required'}), 400

        is_takeover, risk_score, indicators = fraud_detector.detect_account_takeover(data)

        return jsonify({
            'is_account_takeover': is_takeover,
            'risk_score': risk_score * 100,
            'indicators': indicators,
            'recommendation': 'Force logout and require re-authentication' if is_takeover else 'Session appears normal'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze_upi_fraud', methods=['POST'])
def analyze_upi_fraud():
    """Analyze UPI transaction for fraud"""
    try:
        data = request.json

        if not data or 'payment_method' not in data:
            return jsonify({'error': 'Payment method required'}), 400

        is_upi_fraud, risk_score, indicators = fraud_detector.detect_upi_fraud(data)

        return jsonify({
            'is_upi_fraud': is_upi_fraud,
            'risk_score': risk_score * 100,
            'indicators': indicators,
            'recommendation': 'Block transaction and verify with customer' if is_upi_fraud else 'Transaction appears legitimate'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze_identity_theft', methods=['POST'])
def analyze_identity_theft():
    """Analyze identity documents for theft indicators"""
    try:
        data = request.json

        if not data or 'identity_details' not in data:
            return jsonify({'error': 'Identity details required'}), 400

        is_identity_theft, risk_score, indicators = fraud_detector.detect_identity_theft(data)

        return jsonify({
            'is_identity_theft': is_identity_theft,
            'risk_score': risk_score * 100,
            'indicators': indicators,
            'recommendation': 'Request additional verification' if is_identity_theft else 'Identity appears legitimate'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/fraud_statistics', methods=['GET'])
def get_fraud_statistics():
    """Get fraud detection statistics"""
    try:
        cursor = fraud_detector.conn.cursor()

        # Total transactions analyzed
        cursor.execute('SELECT COUNT(*) FROM transactions')
        total_transactions = cursor.fetchone()[0]

        # Fraud detected
        cursor.execute('SELECT COUNT(*) FROM transactions WHERE is_fraud = 1')
        fraud_detected = cursor.fetchone()[0]

        # Fraud by type
        cursor.execute('''
            SELECT fraud_types, COUNT(*) FROM transactions
            WHERE is_fraud = 1 AND fraud_types IS NOT NULL
            GROUP BY fraud_types
        ''')
        fraud_by_type = dict(cursor.fetchall())

        # Average risk score
        cursor.execute('SELECT AVG(risk_score) FROM transactions WHERE is_fraud = 1')
        avg_risk_score = cursor.fetchone()[0] or 0

        # Recent fraud trends (last 7 days)
        cursor.execute('''
            SELECT DATE(timestamp), COUNT(*) FROM transactions
            WHERE is_fraud = 1 AND timestamp > datetime('now', '-7 days')
            GROUP BY DATE(timestamp)
            ORDER BY DATE(timestamp)
        ''')
        recent_trends = dict(cursor.fetchall())

        return jsonify({
            'total_transactions': total_transactions,
            'fraud_detected': fraud_detected,
            'fraud_rate': (fraud_detected / total_transactions * 100) if total_transactions > 0 else 0,
            'fraud_by_type': fraud_by_type,
            'average_risk_score': avg_risk_score,
            'recent_trends': recent_trends,
            'last_updated': datetime.datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/fraud_patterns', methods=['GET'])
def get_fraud_patterns():
    """Get known fraud patterns"""
    try:
        cursor = fraud_detector.conn.cursor()
        cursor.execute('SELECT pattern_type, pattern_data, confidence FROM fraud_patterns')

        patterns = []
        for row in cursor.fetchall():
            patterns.append({
                'type': row[0],
                'pattern': json.loads(row[1]),
                'confidence': row[2]
            })

        return jsonify({'patterns': patterns})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/add_fraud_pattern', methods=['POST'])
def add_fraud_pattern():
    """Add new fraud pattern to database"""
    try:
        data = request.json

        if not data or 'pattern_type' not in data or 'pattern_data' not in data:
            return jsonify({'error': 'Pattern type and data required'}), 400

        cursor = fraud_detector.conn.cursor()
        cursor.execute('''
            INSERT INTO fraud_patterns (pattern_type, pattern_data, confidence)
            VALUES (?, ?, ?)
        ''', (
            data['pattern_type'],
            json.dumps(data['pattern_data']),
            data.get('confidence', 0.8)
        ))
        fraud_detector.conn.commit()

        # Reload patterns
        fraud_detector.load_fraud_patterns()

        return jsonify({'message': 'Fraud pattern added successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/fraud_dashboard')
def fraud_dashboard():
    """Comprehensive fraud detection dashboard"""
    dashboard_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Comprehensive Fraud Detection Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
            .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .stat-number { font-size: 2em; font-weight: bold; color: #e74c3c; }
            .fraud-types { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .test-section { background: white; padding: 20px; border-radius: 8px; }
            .test-form { margin-bottom: 20px; }
            .test-form input, .test-form textarea, .test-form select {
                width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px;
            }
            .test-form button {
                background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;
            }
            .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
            .result.fraud { background: #ffebee; border-left: 4px solid #f44336; }
            .result.safe { background: #e8f5e8; border-left: 4px solid #4caf50; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ Comprehensive Fraud Detection System</h1>
            <p>AI-Powered Detection for All Types of Banking Fraud</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Transactions</h3>
                <div class="stat-number" id="totalTransactions">Loading...</div>
            </div>
            <div class="stat-card">
                <h3>Fraud Detected</h3>
                <div class="stat-number" id="fraudDetected">Loading...</div>
            </div>
            <div class="stat-card">
                <h3>Detection Rate</h3>
                <div class="stat-number" id="detectionRate">Loading...</div>
            </div>
            <div class="stat-card">
                <h3>Avg Risk Score</h3>
                <div class="stat-number" id="avgRiskScore">Loading...</div>
            </div>
        </div>

        <div class="fraud-types">
            <h2>Fraud Types Detected</h2>
            <div id="fraudTypes">Loading...</div>
        </div>

        <div class="test-section">
            <h2>Test Fraud Detection</h2>

            <div class="test-form">
                <h3>Test Phishing Detection</h3>
                <textarea id="phishingContent" placeholder="Enter email/SMS content to analyze..."></textarea>
                <input type="text" id="phishingSender" placeholder="Sender information">
                <button onclick="testPhishing()">Analyze for Phishing</button>
                <div id="phishingResult" class="result"></div>
            </div>

            <div class="test-form">
                <h3>Test Transaction Fraud</h3>
                <input type="number" id="transAmount" placeholder="Transaction Amount">
                <input type="text" id="transMerchant" placeholder="Merchant Name">
                <select id="transPayment">
                    <option value="card">Credit/Debit Card</option>
                    <option value="upi">UPI</option>
                    <option value="netbanking">Net Banking</option>
                    <option value="wallet">Mobile Wallet</option>
                </select>
                <button onclick="testTransaction()">Analyze Transaction</button>
                <div id="transactionResult" class="result"></div>
            </div>

            <div class="test-form">
                <h3>Test Identity Verification</h3>
                <input type="text" id="aadhaarNumber" placeholder="Aadhaar Number (12 digits)">
                <input type="text" id="panNumber" placeholder="PAN Number (e.g., **********)">
                <button onclick="testIdentity()">Verify Identity</button>
                <div id="identityResult" class="result"></div>
            </div>
        </div>

        <script>
            // Load dashboard statistics
            fetch('/api/fraud_statistics')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalTransactions').textContent = data.total_transactions;
                    document.getElementById('fraudDetected').textContent = data.fraud_detected;
                    document.getElementById('detectionRate').textContent = data.fraud_rate.toFixed(2) + '%';
                    document.getElementById('avgRiskScore').textContent = data.average_risk_score.toFixed(1);

                    // Display fraud types
                    let fraudTypesHtml = '';
                    for (const [type, count] of Object.entries(data.fraud_by_type)) {
                        fraudTypesHtml += `<p><strong>${type}:</strong> ${count} cases</p>`;
                    }
                    document.getElementById('fraudTypes').innerHTML = fraudTypesHtml || 'No fraud detected yet';
                });

            function testPhishing() {
                const content = document.getElementById('phishingContent').value;
                const sender = document.getElementById('phishingSender').value;

                fetch('/api/analyze_phishing', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        content: content,
                        sender_info: sender,
                        communication_type: 'email'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    const resultDiv = document.getElementById('phishingResult');
                    resultDiv.className = 'result ' + (data.is_phishing ? 'fraud' : 'safe');
                    resultDiv.innerHTML = `
                        <strong>${data.is_phishing ? '🚨 PHISHING DETECTED' : '✅ LEGITIMATE'}</strong><br>
                        Risk Score: ${data.risk_score.toFixed(1)}%<br>
                        Indicators: ${data.indicators.join(', ')}<br>
                        Recommendation: ${data.recommendation}
                    `;
                });
            }

            function testTransaction() {
                const amount = document.getElementById('transAmount').value;
                const merchant = document.getElementById('transMerchant').value;
                const payment = document.getElementById('transPayment').value;

                fetch('/api/analyze_comprehensive_fraud', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        analysis_type: 'transaction',
                        amount: parseFloat(amount),
                        merchant: merchant,
                        payment_method: payment,
                        user_id: 'test_user',
                        timestamp: new Date().toISOString()
                    })
                })
                .then(response => response.json())
                .then(data => {
                    const resultDiv = document.getElementById('transactionResult');
                    resultDiv.className = 'result ' + (data.is_fraud ? 'fraud' : 'safe');
                    resultDiv.innerHTML = `
                        <strong>${data.is_fraud ? '🚨 FRAUD DETECTED' : '✅ LEGITIMATE'}</strong><br>
                        Risk Score: ${data.risk_score.toFixed(1)}%<br>
                        Fraud Types: ${data.fraud_types.join(', ')}<br>
                        Reasons: ${data.reasons.join(', ')}<br>
                        Actions: ${data.recommended_actions.join(', ')}
                    `;
                });
            }

            function testIdentity() {
                const aadhaar = document.getElementById('aadhaarNumber').value;
                const pan = document.getElementById('panNumber').value;

                fetch('/api/analyze_identity_theft', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        user_id: 'test_user',
                        identity_details: {
                            aadhaar: aadhaar,
                            pan: pan
                        }
                    })
                })
                .then(response => response.json())
                .then(data => {
                    const resultDiv = document.getElementById('identityResult');
                    resultDiv.className = 'result ' + (data.is_identity_theft ? 'fraud' : 'safe');
                    resultDiv.innerHTML = `
                        <strong>${data.is_identity_theft ? '🚨 IDENTITY THEFT RISK' : '✅ IDENTITY VERIFIED'}</strong><br>
                        Risk Score: ${data.risk_score.toFixed(1)}%<br>
                        Indicators: ${data.indicators.join(', ')}<br>
                        Recommendation: ${data.recommendation}
                    `;
                });
            }
        </script>
    </body>
    </html>
    """
    return dashboard_html

if __name__ == '__main__':
    print("🚀 Starting Comprehensive Fraud Detection System...")
    print("📊 Dashboard: http://localhost:5001/api/fraud_dashboard")
    print("🔍 API Endpoints:")
    print("   - POST /api/analyze_comprehensive_fraud")
    print("   - POST /api/analyze_phishing")
    print("   - POST /api/analyze_account_takeover")
    print("   - POST /api/analyze_upi_fraud")
    print("   - POST /api/analyze_identity_theft")
    print("   - GET  /api/fraud_statistics")
    app.run(debug=True, host='0.0.0.0', port=5001)
