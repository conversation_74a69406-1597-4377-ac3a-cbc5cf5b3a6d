#!/usr/bin/env python3
"""
Real-Time Banking System with Fraud Detection and Payment Cancellation
Automatically detects fraud, cancels payments, and sends alerts to users
"""

import json
import datetime
import sqlite3
import uuid
import time
import threading
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

# ML Libraries
import numpy as np
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer

# Flask and API
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

class TransactionStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FRAUD_BLOCKED = "fraud_blocked"
    FAILED = "failed"

class AlertType(Enum):
    FRAUD_DETECTED = "fraud_detected"
    PAYMENT_CANCELLED = "payment_cancelled"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    ACCOUNT_SECURITY = "account_security"

@dataclass
class FraudResult:
    is_fraud: bool
    risk_score: float
    fraud_reasons: List[str]
    recommended_action: str
    confidence: float

@dataclass
class TransactionResult:
    success: bool
    transaction_id: str
    status: TransactionStatus
    message: str
    fraud_result: FraudResult = None
    alert_sent: bool = False

class RealTimeBankingFraudSystem:
    def __init__(self):
        self.setup_database()
        self.initialize_fraud_models()
        self.initialize_demo_accounts()
        self.transaction_lock = threading.Lock()

    def setup_database(self):
        """Setup comprehensive database for banking and fraud detection"""
        self.conn = sqlite3.connect('banking_fraud_system.db', check_same_thread=False)
        cursor = self.conn.cursor()

        # Enhanced accounts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT UNIQUE NOT NULL,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT NOT NULL,
                account_number TEXT UNIQUE NOT NULL,
                routing_number TEXT NOT NULL,
                balance REAL DEFAULT 0.0,
                daily_limit REAL DEFAULT 50000.0,
                status TEXT DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                fraud_score REAL DEFAULT 0.0
            )
        ''')

        # Enhanced transactions table with fraud details
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id TEXT UNIQUE NOT NULL,
                user_id TEXT NOT NULL,
                recipient_account TEXT,
                recipient_name TEXT,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                balance_before REAL NOT NULL,
                balance_after REAL NOT NULL,
                merchant TEXT,
                description TEXT,
                payment_method TEXT,
                location TEXT,
                ip_address TEXT,
                device_info TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'pending',
                fraud_checked INTEGER DEFAULT 0,
                is_fraud INTEGER DEFAULT 0,
                fraud_score REAL DEFAULT 0.0,
                fraud_reasons TEXT,
                cancellation_reason TEXT,
                processing_time REAL DEFAULT 0.0,
                FOREIGN KEY (user_id) REFERENCES accounts (user_id)
            )
        ''')

        # User alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                transaction_id TEXT,
                alert_type TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                severity TEXT DEFAULT 'medium',
                sent_via TEXT DEFAULT 'system',
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                read_status INTEGER DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES accounts (user_id),
                FOREIGN KEY (transaction_id) REFERENCES transactions (transaction_id)
            )
        ''')

        # Fraud patterns and rules
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fraud_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rule_name TEXT NOT NULL,
                rule_type TEXT NOT NULL,
                conditions TEXT NOT NULL,
                action TEXT NOT NULL,
                risk_weight REAL DEFAULT 1.0,
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # User behavior patterns
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_behavior (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                avg_transaction_amount REAL DEFAULT 0.0,
                max_daily_amount REAL DEFAULT 0.0,
                common_merchants TEXT,
                usual_transaction_times TEXT,
                typical_locations TEXT,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES accounts (user_id)
            )
        ''')

        self.conn.commit()
        self.initialize_fraud_rules()

    def initialize_fraud_rules(self):
        """Initialize default fraud detection rules"""
        cursor = self.conn.cursor()

        # Check if rules already exist
        cursor.execute('SELECT COUNT(*) FROM fraud_rules')
        if cursor.fetchone()[0] > 0:
            return

        default_rules = [
            {
                'rule_name': 'High Amount Transaction',
                'rule_type': 'amount_threshold',
                'conditions': json.dumps({'min_amount': 100000, 'max_amount': 1000000}),
                'action': 'block_and_alert',
                'risk_weight': 0.8
            },
            {
                'rule_name': 'Rapid Multiple Transactions',
                'rule_type': 'velocity',
                'conditions': json.dumps({'max_transactions': 5, 'time_window': 300}),
                'action': 'block_and_alert',
                'risk_weight': 0.7
            },
            {
                'rule_name': 'Unusual Time Transaction',
                'rule_type': 'time_pattern',
                'conditions': json.dumps({'blocked_hours': [0, 1, 2, 3, 4, 5]}),
                'action': 'alert_and_review',
                'risk_weight': 0.4
            },
            {
                'rule_name': 'Round Amount Suspicion',
                'rule_type': 'amount_pattern',
                'conditions': json.dumps({'round_amounts': [10000, 50000, 100000]}),
                'action': 'alert_and_review',
                'risk_weight': 0.3
            },
            {
                'rule_name': 'New Device High Amount',
                'rule_type': 'device_risk',
                'conditions': json.dumps({'new_device': True, 'min_amount': 25000}),
                'action': 'block_and_alert',
                'risk_weight': 0.6
            }
        ]

        for rule in default_rules:
            cursor.execute('''
                INSERT INTO fraud_rules (rule_name, rule_type, conditions, action, risk_weight)
                VALUES (?, ?, ?, ?, ?)
            ''', (rule['rule_name'], rule['rule_type'], rule['conditions'],
                  rule['action'], rule['risk_weight']))

        self.conn.commit()
        print("Fraud detection rules initialized!")

    def initialize_fraud_models(self):
        """Initialize ML models for fraud detection"""
        # Simple fraud detection model
        self.fraud_model = RandomForestClassifier(n_estimators=50, random_state=42)
        self.anomaly_model = IsolationForest(contamination=0.1, random_state=42)
        self.scaler = StandardScaler()

        # Generate synthetic training data
        self.train_fraud_models()

    def train_fraud_models(self):
        """Train fraud detection models with synthetic data"""
        np.random.seed(42)

        # Generate training features: [amount, hour, day_of_week, is_round_amount, velocity_score]
        normal_transactions = []
        fraud_transactions = []

        # Normal transactions (80%)
        for _ in range(800):
            amount = np.random.lognormal(8, 1)  # Normal amounts
            hour = np.random.normal(14, 4)  # Business hours
            hour = max(0, min(23, hour))
            day_of_week = np.random.randint(0, 7)
            is_round = 0 if amount % 1000 != 0 else 1
            velocity = np.random.normal(0.2, 0.1)

            normal_transactions.append([amount, hour, day_of_week, is_round, velocity, 0])

        # Fraudulent transactions (20%)
        for _ in range(200):
            amount = np.random.lognormal(10, 1.5)  # Higher amounts
            hour = np.random.choice([1, 2, 3, 23, 0])  # Unusual hours
            day_of_week = np.random.randint(0, 7)
            is_round = 1  # Often round amounts
            velocity = np.random.normal(0.8, 0.2)  # High velocity

            fraud_transactions.append([amount, hour, day_of_week, is_round, velocity, 1])

        # Combine and train
        all_data = np.array(normal_transactions + fraud_transactions)
        X = all_data[:, :-1]
        y = all_data[:, -1]

        # Scale features
        X_scaled = self.scaler.fit_transform(X)

        # Train models
        self.fraud_model.fit(X_scaled, y)
        self.anomaly_model.fit(X_scaled[y == 0])  # Train on normal data only

        print("Fraud detection models trained successfully!")

    def initialize_demo_accounts(self):
        """Initialize demo accounts with realistic data"""
        cursor = self.conn.cursor()

        # Check if accounts exist
        cursor.execute('SELECT COUNT(*) FROM accounts')
        if cursor.fetchone()[0] > 0:
            return

        demo_accounts = [
            {
                'user_id': 'user_001',
                'username': 'john_doe',
                'password': 'password123',
                'full_name': 'John Doe',
                'email': '<EMAIL>',
                'phone': '+91-**********',
                'account_number': '****************',
                'routing_number': '*********',
                'balance': 250000.00,
                'daily_limit': 100000.00
            },
            {
                'user_id': 'user_002',
                'username': 'jane_smith',
                'password': 'password456',
                'full_name': 'Jane Smith',
                'email': '<EMAIL>',
                'phone': '+91-**********',
                'account_number': '****************',
                'routing_number': '*********',
                'balance': 150000.00,
                'daily_limit': 75000.00
            }
        ]

        for account in demo_accounts:
            cursor.execute('''
                INSERT INTO accounts (user_id, username, password, full_name, email, phone,
                                    account_number, routing_number, balance, daily_limit)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                account['user_id'], account['username'], account['password'],
                account['full_name'], account['email'], account['phone'],
                account['account_number'], account['routing_number'],
                account['balance'], account['daily_limit']
            ))

        self.conn.commit()
        print("Demo accounts initialized!")

    def authenticate_user(self, username: str, password: str) -> Dict:
        """Authenticate user and return account info"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT user_id, username, full_name, email, phone, account_number,
                   balance, daily_limit, status
            FROM accounts WHERE username = ? AND password = ? AND status = 'active'
        ''', (username, password))

        row = cursor.fetchone()
        if row:
            return {
                'user_id': row[0],
                'username': row[1],
                'full_name': row[2],
                'email': row[3],
                'phone': row[4],
                'account_number': row[5],
                'balance': row[6],
                'daily_limit': row[7],
                'status': row[8]
            }
        return None

    def extract_transaction_features(self, transaction_data: Dict) -> np.array:
        """Extract features for fraud detection"""
        amount = float(transaction_data.get('amount', 0))
        timestamp = datetime.datetime.fromisoformat(
            transaction_data.get('timestamp', datetime.datetime.now().isoformat())
        )

        hour = timestamp.hour
        day_of_week = timestamp.weekday()
        is_round_amount = 1 if amount % 1000 == 0 else 0
        velocity_score = self.calculate_velocity_score(transaction_data.get('user_id', ''))

        return np.array([[amount, hour, day_of_week, is_round_amount, velocity_score]])

    def calculate_velocity_score(self, user_id: str) -> float:
        """Calculate transaction velocity score"""
        cursor = self.conn.cursor()

        # Count transactions in last hour
        one_hour_ago = datetime.datetime.now() - datetime.timedelta(hours=1)
        cursor.execute('''
            SELECT COUNT(*) FROM transactions
            WHERE user_id = ? AND timestamp > ? AND status != 'cancelled'
        ''', (user_id, one_hour_ago))

        recent_count = cursor.fetchone()[0]

        # Normalize to 0-1 scale
        return min(recent_count / 10.0, 1.0)

    def check_fraud_rules(self, transaction_data: Dict) -> FraudResult:
        """Check transaction against fraud rules"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM fraud_rules WHERE is_active = 1')

        fraud_reasons = []
        total_risk_score = 0.0
        max_action = "allow"

        for rule in cursor.fetchall():
            rule_id, rule_name, rule_type, conditions_json, action, risk_weight, _, _ = rule
            conditions = json.loads(conditions_json)

            rule_triggered = False

            # Check different rule types
            if rule_type == 'amount_threshold':
                amount = float(transaction_data.get('amount', 0))
                if conditions.get('min_amount', 0) <= amount <= conditions.get('max_amount', float('inf')):
                    rule_triggered = True
                    fraud_reasons.append(f"High amount transaction: ₹{amount:,.2f}")

            elif rule_type == 'velocity':
                user_id = transaction_data.get('user_id', '')
                velocity_score = self.calculate_velocity_score(user_id)
                max_transactions = conditions.get('max_transactions', 5)
                if velocity_score >= max_transactions / 10.0:
                    rule_triggered = True
                    fraud_reasons.append(f"Rapid transactions detected: {int(velocity_score * 10)} in last hour")

            elif rule_type == 'time_pattern':
                timestamp = datetime.datetime.fromisoformat(
                    transaction_data.get('timestamp', datetime.datetime.now().isoformat())
                )
                blocked_hours = conditions.get('blocked_hours', [])
                if timestamp.hour in blocked_hours:
                    rule_triggered = True
                    fraud_reasons.append(f"Transaction at unusual time: {timestamp.hour}:00")

            elif rule_type == 'amount_pattern':
                amount = float(transaction_data.get('amount', 0))
                round_amounts = conditions.get('round_amounts', [])
                if any(abs(amount - ra) < 100 for ra in round_amounts):
                    rule_triggered = True
                    fraud_reasons.append(f"Suspicious round amount: ₹{amount:,.2f}")

            elif rule_type == 'device_risk':
                # Simplified device check
                device_info = transaction_data.get('device_info', '')
                amount = float(transaction_data.get('amount', 0))
                if 'new' in device_info.lower() and amount >= conditions.get('min_amount', 25000):
                    rule_triggered = True
                    fraud_reasons.append("High amount transaction from new device")

            if rule_triggered:
                total_risk_score += risk_weight
                if action == 'block_and_alert' and max_action != 'block_and_alert':
                    max_action = 'block_and_alert'
                elif action == 'alert_and_review' and max_action == 'allow':
                    max_action = 'alert_and_review'

        # ML model check
        features = self.extract_transaction_features(transaction_data)
        features_scaled = self.scaler.transform(features)

        fraud_probability = self.fraud_model.predict_proba(features_scaled)[0][1]
        anomaly_score = self.anomaly_model.decision_function(features_scaled)[0]

        if fraud_probability > 0.7:
            total_risk_score += 0.8
            fraud_reasons.append(f"ML model detected fraud (confidence: {fraud_probability:.2f})")
            max_action = 'block_and_alert'
        elif fraud_probability > 0.5:
            total_risk_score += 0.4
            fraud_reasons.append(f"ML model suspicious activity (confidence: {fraud_probability:.2f})")
            if max_action == 'allow':
                max_action = 'alert_and_review'

        if anomaly_score < -0.2:
            total_risk_score += 0.3
            fraud_reasons.append("Transaction pattern anomaly detected")
            if max_action == 'allow':
                max_action = 'alert_and_review'

        # Normalize risk score to 0-100
        risk_score = min(total_risk_score * 100, 100)
        is_fraud = max_action == 'block_and_alert'

        return FraudResult(
            is_fraud=is_fraud,
            risk_score=risk_score,
            fraud_reasons=fraud_reasons,
            recommended_action=max_action,
            confidence=fraud_probability if fraud_reasons else 0.0
        )

    def process_transaction(self, transaction_data: Dict) -> TransactionResult:
        """Process transaction with real-time fraud detection"""
        start_time = time.time()
        transaction_id = str(uuid.uuid4()).replace('-', '').upper()[:12]

        with self.transaction_lock:
            try:
                cursor = self.conn.cursor()

                # Get user account info
                user_id = transaction_data['user_id']
                cursor.execute('SELECT balance, daily_limit FROM accounts WHERE user_id = ?', (user_id,))
                account_info = cursor.fetchone()

                if not account_info:
                    return TransactionResult(
                        success=False,
                        transaction_id=transaction_id,
                        status=TransactionStatus.FAILED,
                        message="Account not found"
                    )

                current_balance, daily_limit = account_info
                amount = float(transaction_data['amount'])

                # Check sufficient funds
                if current_balance < amount:
                    return TransactionResult(
                        success=False,
                        transaction_id=transaction_id,
                        status=TransactionStatus.FAILED,
                        message="Insufficient funds"
                    )

                # Check daily limit
                today = datetime.datetime.now().date()
                cursor.execute('''
                    SELECT COALESCE(SUM(amount), 0) FROM transactions
                    WHERE user_id = ? AND DATE(timestamp) = ? AND status = 'completed'
                ''', (user_id, today))

                daily_spent = cursor.fetchone()[0]
                if daily_spent + amount > daily_limit:
                    return TransactionResult(
                        success=False,
                        transaction_id=transaction_id,
                        status=TransactionStatus.FAILED,
                        message=f"Daily limit exceeded. Limit: ₹{daily_limit:,.2f}, Spent: ₹{daily_spent:,.2f}"
                    )

                # FRAUD DETECTION - This is the key part!
                transaction_data['transaction_id'] = transaction_id
                fraud_result = self.check_fraud_rules(transaction_data)

                processing_time = time.time() - start_time

                # Store transaction with fraud check results
                new_balance = current_balance - amount if not fraud_result.is_fraud else current_balance
                status = TransactionStatus.FRAUD_BLOCKED if fraud_result.is_fraud else TransactionStatus.COMPLETED

                cursor.execute('''
                    INSERT INTO transactions (
                        transaction_id, user_id, recipient_account, recipient_name,
                        transaction_type, amount, balance_before, balance_after,
                        merchant, description, payment_method, location, ip_address,
                        device_info, status, fraud_checked, is_fraud, fraud_score,
                        fraud_reasons, processing_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    transaction_id, user_id,
                    transaction_data.get('recipient_account', ''),
                    transaction_data.get('recipient_name', ''),
                    transaction_data.get('transaction_type', 'transfer'),
                    amount, current_balance, new_balance,
                    transaction_data.get('merchant', ''),
                    transaction_data.get('description', ''),
                    transaction_data.get('payment_method', 'online'),
                    transaction_data.get('location', ''),
                    transaction_data.get('ip_address', ''),
                    transaction_data.get('device_info', ''),
                    status.value, 1, 1 if fraud_result.is_fraud else 0,
                    fraud_result.risk_score,
                    json.dumps(fraud_result.fraud_reasons),
                    processing_time
                ))

                # Update balance only if transaction is not blocked
                if not fraud_result.is_fraud:
                    cursor.execute('UPDATE accounts SET balance = ? WHERE user_id = ?',
                                 (new_balance, user_id))

                self.conn.commit()

                # Send alerts based on fraud detection results
                alert_sent = self.send_fraud_alert(user_id, transaction_id, fraud_result, transaction_data)

                # Prepare result message
                if fraud_result.is_fraud:
                    message = f"🚨 PAYMENT CANCELLED - Fraud Detected!\nReasons: {', '.join(fraud_result.fraud_reasons)}\nRisk Score: {fraud_result.risk_score:.1f}%"
                elif fraud_result.risk_score > 30:
                    message = f"⚠️ Payment completed but flagged for review\nRisk Score: {fraud_result.risk_score:.1f}%"
                else:
                    message = f"✅ Payment completed successfully\nAmount: ₹{amount:,.2f}"

                return TransactionResult(
                    success=not fraud_result.is_fraud,
                    transaction_id=transaction_id,
                    status=status,
                    message=message,
                    fraud_result=fraud_result,
                    alert_sent=alert_sent
                )

            except Exception as e:
                self.conn.rollback()
                return TransactionResult(
                    success=False,
                    transaction_id=transaction_id,
                    status=TransactionStatus.FAILED,
                    message=f"Transaction failed: {str(e)}"
                )

    def send_fraud_alert(self, user_id: str, transaction_id: str, fraud_result: FraudResult,
                        transaction_data: Dict) -> bool:
        """Send fraud alert to user via multiple channels"""
        try:
            cursor = self.conn.cursor()

            # Get user contact info
            cursor.execute('SELECT full_name, email, phone FROM accounts WHERE user_id = ?', (user_id,))
            user_info = cursor.fetchone()

            if not user_info:
                return False

            full_name, email, phone = user_info
            amount = transaction_data.get('amount', 0)

            # Determine alert type and message based on fraud result
            if fraud_result.is_fraud:
                alert_type = AlertType.FRAUD_DETECTED.value
                title = "🚨 FRAUD ALERT - Payment Cancelled"
                message = f"""
Dear {full_name},

We have CANCELLED a suspicious transaction on your account for your protection.

Transaction Details:
• Amount: ₹{amount:,.2f}
• Recipient: {transaction_data.get('recipient_name', 'Unknown')}
• Time: {datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
• Transaction ID: {transaction_id}

Fraud Indicators:
{chr(10).join(['• ' + reason for reason in fraud_result.fraud_reasons])}

Risk Score: {fraud_result.risk_score:.1f}%

IMPORTANT: Your account is safe. No money has been debited.

If you attempted this transaction:
1. Contact customer care immediately: 1800-XXX-XXXX
2. Visit nearest branch with ID proof
3. Consider changing your banking passwords

If you did NOT attempt this transaction:
1. Your account security is intact
2. Monitor your account for any unusual activity
3. Report any suspicious communications

Stay Safe!
SecureBank Security Team
                """
                severity = "high"

            elif fraud_result.risk_score > 50:
                alert_type = AlertType.SUSPICIOUS_ACTIVITY.value
                title = "⚠️ Security Alert - Transaction Under Review"
                message = f"""
Dear {full_name},

We have processed your transaction but flagged it for security review.

Transaction Details:
• Amount: ₹{amount:,.2f}
• Recipient: {transaction_data.get('recipient_name', 'Unknown')}
• Time: {datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
• Transaction ID: {transaction_id}
• Status: Completed but under review

Security Indicators:
{chr(10).join(['• ' + reason for reason in fraud_result.fraud_reasons])}

Risk Score: {fraud_result.risk_score:.1f}%

Your transaction has been completed successfully. This is a routine security check.

If you did not authorize this transaction, please contact us immediately at 1800-XXX-XXXX.

SecureBank Security Team
                """
                severity = "medium"

            else:
                # Low risk - no alert needed
                return True

            # Store alert in database
            cursor.execute('''
                INSERT INTO user_alerts (user_id, transaction_id, alert_type, title, message, severity)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, transaction_id, alert_type, title, message, severity))

            self.conn.commit()

            # Simulate sending alerts via different channels
            self.send_email_alert(email, title, message)
            self.send_sms_alert(phone, title, amount, fraud_result.is_fraud)
            self.send_push_notification(user_id, title, fraud_result.is_fraud)

            return True

        except Exception as e:
            print(f"Error sending fraud alert: {e}")
            return False

    def send_email_alert(self, email: str, title: str, message: str):
        """Simulate sending email alert"""
        print(f"\n📧 EMAIL ALERT SENT TO: {email}")
        print(f"Subject: {title}")
        print(f"Message: {message[:200]}...")
        print("=" * 50)

    def send_sms_alert(self, phone: str, title: str, amount: float, is_fraud: bool):
        """Simulate sending SMS alert"""
        if is_fraud:
            sms_message = f"FRAUD ALERT: Transaction of ₹{amount:,.2f} CANCELLED for security. Your account is safe. Call 1800-XXX-XXXX if needed. -SecureBank"
        else:
            sms_message = f"Security Alert: Transaction of ₹{amount:,.2f} completed but under review. Call 1800-XXX-XXXX if unauthorized. -SecureBank"

        print(f"\n📱 SMS ALERT SENT TO: {phone}")
        print(f"Message: {sms_message}")
        print("=" * 50)

    def send_push_notification(self, user_id: str, title: str, is_fraud: bool):
        """Simulate sending push notification"""
        notification_type = "FRAUD_BLOCKED" if is_fraud else "SECURITY_REVIEW"
        print(f"\n🔔 PUSH NOTIFICATION SENT TO USER: {user_id}")
        print(f"Type: {notification_type}")
        print(f"Title: {title}")
        print("=" * 50)

    def get_user_alerts(self, user_id: str, limit: int = 10) -> List[Dict]:
        """Get recent alerts for user"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT transaction_id, alert_type, title, message, severity, timestamp, read_status
            FROM user_alerts WHERE user_id = ?
            ORDER BY timestamp DESC LIMIT ?
        ''', (user_id, limit))

        alerts = []
        for row in cursor.fetchall():
            alerts.append({
                'transaction_id': row[0],
                'alert_type': row[1],
                'title': row[2],
                'message': row[3],
                'severity': row[4],
                'timestamp': row[5],
                'read_status': bool(row[6])
            })

        return alerts

    def get_transaction_history(self, user_id: str, limit: int = 20) -> List[Dict]:
        """Get transaction history with fraud details"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT transaction_id, recipient_account, recipient_name, transaction_type,
                   amount, balance_after, merchant, description, timestamp, status,
                   is_fraud, fraud_score, fraud_reasons
            FROM transactions WHERE user_id = ?
            ORDER BY timestamp DESC LIMIT ?
        ''', (user_id, limit))

        transactions = []
        for row in cursor.fetchall():
            fraud_reasons = json.loads(row[12]) if row[12] else []
            transactions.append({
                'transaction_id': row[0],
                'recipient_account': row[1],
                'recipient_name': row[2],
                'transaction_type': row[3],
                'amount': row[4],
                'balance_after': row[5],
                'merchant': row[6],
                'description': row[7],
                'timestamp': row[8],
                'status': row[9],
                'is_fraud': bool(row[10]),
                'fraud_score': row[11],
                'fraud_reasons': fraud_reasons
            })

        return transactions

# Initialize the banking fraud system
banking_system = RealTimeBankingFraudSystem()

# Flask API Endpoints

@app.route('/')
def index():
    """Serve the banking application"""
    with open('index.html', 'r') as f:
        return f.read()

@app.route('/api/login', methods=['POST'])
def login():
    """User authentication"""
    try:
        data = request.json
        username = data.get('username')
        password = data.get('password')

        user = banking_system.authenticate_user(username, password)
        if user:
            return jsonify({
                'success': True,
                'user': user,
                'message': 'Login successful'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Invalid username or password'
            }), 401

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/process_transaction', methods=['POST'])
def process_transaction():
    """Process transaction with fraud detection"""
    try:
        transaction_data = request.json

        # Add timestamp and IP info
        transaction_data['timestamp'] = datetime.datetime.now().isoformat()
        transaction_data['ip_address'] = request.remote_addr
        transaction_data['device_info'] = request.headers.get('User-Agent', 'Unknown')

        # Process transaction with fraud detection
        result = banking_system.process_transaction(transaction_data)

        response = {
            'success': result.success,
            'transaction_id': result.transaction_id,
            'status': result.status.value,
            'message': result.message,
            'alert_sent': result.alert_sent
        }

        if result.fraud_result:
            response['fraud_details'] = {
                'is_fraud': result.fraud_result.is_fraud,
                'risk_score': result.fraud_result.risk_score,
                'fraud_reasons': result.fraud_result.fraud_reasons,
                'recommended_action': result.fraud_result.recommended_action,
                'confidence': result.fraud_result.confidence
            }

        return jsonify(response)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Transaction processing failed: {str(e)}'
        }), 500

@app.route('/api/get_alerts/<user_id>')
def get_alerts(user_id):
    """Get user alerts"""
    try:
        alerts = banking_system.get_user_alerts(user_id)
        return jsonify({'alerts': alerts})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/get_transactions/<user_id>')
def get_transactions(user_id):
    """Get transaction history"""
    try:
        transactions = banking_system.get_transaction_history(user_id)
        return jsonify({'transactions': transactions})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/get_balance/<user_id>')
def get_balance(user_id):
    """Get current account balance"""
    try:
        cursor = banking_system.conn.cursor()
        cursor.execute('SELECT balance FROM accounts WHERE user_id = ?', (user_id,))
        result = cursor.fetchone()

        if result:
            return jsonify({'balance': result[0]})
        else:
            return jsonify({'error': 'Account not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/fraud_dashboard')
def fraud_dashboard():
    """Banking fraud detection dashboard"""
    dashboard_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Real-Time Banking Fraud Detection System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .card { background: white; border-radius: 10px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .login-section { max-width: 400px; margin: 0 auto; }
            .dashboard-section { display: none; }
            .form-group { margin-bottom: 15px; }
            .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
            .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
            .btn-primary { background: #667eea; color: white; }
            .btn-danger { background: #dc3545; color: white; }
            .btn-success { background: #28a745; color: white; }
            .alert { padding: 15px; border-radius: 5px; margin: 10px 0; }
            .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
            .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
            .stat-number { font-size: 2em; font-weight: bold; }
            .transaction-form { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
            .transaction-result { margin-top: 20px; padding: 15px; border-radius: 5px; }
            .fraud-blocked { background: #f8d7da; border-left: 4px solid #dc3545; }
            .fraud-warning { background: #fff3cd; border-left: 4px solid #ffc107; }
            .fraud-safe { background: #d4edda; border-left: 4px solid #28a745; }
            .transaction-history { max-height: 400px; overflow-y: auto; }
            .transaction-item { padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
            .transaction-item.fraud { background: #fff5f5; border-left: 3px solid #dc3545; }
            .hidden { display: none; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛡️ Real-Time Banking Fraud Detection System</h1>
            <p>Advanced AI-Powered Fraud Prevention & Payment Cancellation</p>
        </div>

        <div class="container">
            <!-- Login Section -->
            <div id="loginSection" class="login-section">
                <div class="card">
                    <h2>Banking Login</h2>
                    <form id="loginForm">
                        <div class="form-group">
                            <label>Username</label>
                            <input type="text" id="username" value="john_doe" required>
                        </div>
                        <div class="form-group">
                            <label>Password</label>
                            <input type="password" id="password" value="password123" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Login</button>
                    </form>
                    <div class="alert alert-warning" style="margin-top: 15px;">
                        <strong>Demo Accounts:</strong><br>
                        Username: john_doe, Password: password123<br>
                        Username: jane_smith, Password: password456
                    </div>
                </div>
            </div>

            <!-- Dashboard Section -->
            <div id="dashboardSection" class="dashboard-section">
                <div class="card">
                    <h2>Welcome, <span id="userName"></span></h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="accountBalance">₹0</div>
                            <div>Account Balance</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalTransactions">0</div>
                            <div>Total Transactions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="fraudBlocked">0</div>
                            <div>Fraud Blocked</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="alertsCount">0</div>
                            <div>Security Alerts</div>
                        </div>
                    </div>
                </div>

                <!-- Transaction Form -->
                <div class="card">
                    <h3>🔄 Make a Transaction (Real-Time Fraud Detection)</h3>
                    <form id="transactionForm">
                        <div class="transaction-form">
                            <div class="form-group">
                                <label>Recipient Account Number</label>
                                <input type="text" id="recipientAccount" placeholder="****************" required>
                            </div>
                            <div class="form-group">
                                <label>Recipient Name</label>
                                <input type="text" id="recipientName" placeholder="John Smith" required>
                            </div>
                            <div class="form-group">
                                <label>Amount (₹)</label>
                                <input type="number" id="amount" placeholder="1000" min="1" required>
                            </div>
                            <div class="form-group">
                                <label>Transaction Type</label>
                                <select id="transactionType">
                                    <option value="transfer">Bank Transfer</option>
                                    <option value="upi">UPI Payment</option>
                                    <option value="payment">Bill Payment</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Description</label>
                                <input type="text" id="description" placeholder="Payment description">
                            </div>
                            <div class="form-group">
                                <label>Payment Method</label>
                                <select id="paymentMethod">
                                    <option value="online">Online Banking</option>
                                    <option value="upi">UPI</option>
                                    <option value="card">Debit Card</option>
                                </select>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Process Transaction</button>
                        <button type="button" class="btn btn-danger" onclick="testFraudTransaction()">Test Fraud Transaction</button>
                    </form>

                    <div id="transactionResult" class="transaction-result hidden"></div>
                </div>

                <!-- Quick Fraud Tests -->
                <div class="card">
                    <h3>🧪 Quick Fraud Tests</h3>
                    <button class="btn btn-danger" onclick="testHighAmount()">Test High Amount (₹2,00,000)</button>
                    <button class="btn btn-danger" onclick="testRapidTransactions()">Test Rapid Transactions</button>
                    <button class="btn btn-danger" onclick="testUnusualTime()">Test Unusual Time</button>
                    <button class="btn btn-danger" onclick="testRoundAmount()">Test Round Amount</button>
                </div>

                <!-- Transaction History -->
                <div class="card">
                    <h3>📊 Recent Transactions</h3>
                    <div id="transactionHistory" class="transaction-history">
                        Loading...
                    </div>
                </div>

                <!-- Security Alerts -->
                <div class="card">
                    <h3>🚨 Security Alerts</h3>
                    <div id="securityAlerts">
                        Loading...
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentUser = null;

            // Login functionality
            document.getElementById('loginForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                try {
                    const response = await fetch('/api/login', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify({username, password})
                    });

                    const result = await response.json();

                    if (result.success) {
                        currentUser = result.user;
                        showDashboard();
                    } else {
                        alert('Login failed: ' + result.message);
                    }
                } catch (error) {
                    alert('Login error: ' + error.message);
                }
            });

            // Show dashboard after login
            function showDashboard() {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboardSection').style.display = 'block';
                document.getElementById('userName').textContent = currentUser.full_name;

                updateDashboard();
                loadTransactionHistory();
                loadSecurityAlerts();
            }

            // Update dashboard stats
            async function updateDashboard() {
                try {
                    // Update balance
                    const balanceResponse = await fetch(`/api/get_balance/${currentUser.user_id}`);
                    const balanceData = await balanceResponse.json();
                    document.getElementById('accountBalance').textContent = `₹${balanceData.balance.toLocaleString()}`;

                    // Update transaction stats
                    const transResponse = await fetch(`/api/get_transactions/${currentUser.user_id}`);
                    const transData = await transResponse.json();

                    const transactions = transData.transactions || [];
                    const fraudBlocked = transactions.filter(t => t.is_fraud).length;

                    document.getElementById('totalTransactions').textContent = transactions.length;
                    document.getElementById('fraudBlocked').textContent = fraudBlocked;

                    // Update alerts count
                    const alertsResponse = await fetch(`/api/get_alerts/${currentUser.user_id}`);
                    const alertsData = await alertsResponse.json();
                    document.getElementById('alertsCount').textContent = alertsData.alerts.length;

                } catch (error) {
                    console.error('Error updating dashboard:', error);
                }
            }

            // Process transaction
            document.getElementById('transactionForm').addEventListener('submit', async (e) => {
                e.preventDefault();

                const transactionData = {
                    user_id: currentUser.user_id,
                    recipient_account: document.getElementById('recipientAccount').value,
                    recipient_name: document.getElementById('recipientName').value,
                    amount: parseFloat(document.getElementById('amount').value),
                    transaction_type: document.getElementById('transactionType').value,
                    description: document.getElementById('description').value,
                    payment_method: document.getElementById('paymentMethod').value
                };

                await processTransaction(transactionData);
            });

            async function processTransaction(transactionData) {
                try {
                    const response = await fetch('/api/process_transaction', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify(transactionData)
                    });

                    const result = await response.json();

                    // Show result
                    const resultDiv = document.getElementById('transactionResult');
                    resultDiv.className = 'transaction-result';
                    resultDiv.classList.remove('hidden');

                    if (result.fraud_details && result.fraud_details.is_fraud) {
                        resultDiv.classList.add('fraud-blocked');
                        resultDiv.innerHTML = `
                            <h4>🚨 TRANSACTION CANCELLED - FRAUD DETECTED</h4>
                            <p><strong>Status:</strong> ${result.status}</p>
                            <p><strong>Risk Score:</strong> ${result.fraud_details.risk_score.toFixed(1)}%</p>
                            <p><strong>Reasons:</strong></p>
                            <ul>${result.fraud_details.fraud_reasons.map(r => '<li>' + r + '</li>').join('')}</ul>
                            <p><strong>Action:</strong> ${result.fraud_details.recommended_action}</p>
                            <p><strong>Alert Sent:</strong> ${result.alert_sent ? 'Yes' : 'No'}</p>
                            <div class="alert alert-danger">
                                <strong>Your account is safe!</strong> No money has been debited.
                                If you attempted this transaction, please contact customer care.
                            </div>
                        `;
                    } else if (result.fraud_details && result.fraud_details.risk_score > 30) {
                        resultDiv.classList.add('fraud-warning');
                        resultDiv.innerHTML = `
                            <h4>⚠️ TRANSACTION COMPLETED - UNDER REVIEW</h4>
                            <p><strong>Status:</strong> ${result.status}</p>
                            <p><strong>Risk Score:</strong> ${result.fraud_details.risk_score.toFixed(1)}%</p>
                            <p><strong>Transaction ID:</strong> ${result.transaction_id}</p>
                            <div class="alert alert-warning">
                                Transaction completed but flagged for security review.
                            </div>
                        `;
                    } else {
                        resultDiv.classList.add('fraud-safe');
                        resultDiv.innerHTML = `
                            <h4>✅ TRANSACTION SUCCESSFUL</h4>
                            <p><strong>Transaction ID:</strong> ${result.transaction_id}</p>
                            <p><strong>Status:</strong> ${result.status}</p>
                            <p><strong>Amount:</strong> ₹${transactionData.amount.toLocaleString()}</p>
                            <div class="alert alert-success">
                                Transaction completed successfully!
                            </div>
                        `;
                    }

                    // Refresh dashboard
                    updateDashboard();
                    loadTransactionHistory();
                    loadSecurityAlerts();

                } catch (error) {
                    const resultDiv = document.getElementById('transactionResult');
                    resultDiv.className = 'transaction-result fraud-blocked';
                    resultDiv.classList.remove('hidden');
                    resultDiv.innerHTML = `<h4>❌ TRANSACTION FAILED</h4><p>Error: ${error.message}</p>`;
                }
            }

            // Quick fraud tests
            async function testHighAmount() {
                await processTransaction({
                    user_id: currentUser.user_id,
                    recipient_account: '****************',
                    recipient_name: 'Test Recipient',
                    amount: 200000,
                    transaction_type: 'transfer',
                    description: 'High amount test',
                    payment_method: 'online'
                });
            }

            async function testRapidTransactions() {
                for (let i = 0; i < 6; i++) {
                    await processTransaction({
                        user_id: currentUser.user_id,
                        recipient_account: '****************',
                        recipient_name: 'Rapid Test',
                        amount: 1000,
                        transaction_type: 'transfer',
                        description: `Rapid transaction ${i+1}`,
                        payment_method: 'online'
                    });
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            async function testUnusualTime() {
                const transactionData = {
                    user_id: currentUser.user_id,
                    recipient_account: '****************',
                    recipient_name: 'Night Test',
                    amount: 5000,
                    transaction_type: 'transfer',
                    description: 'Unusual time test',
                    payment_method: 'online',
                    timestamp: new Date().toISOString().replace(/T\d{2}:/, 'T02:') // Force 2 AM
                };
                await processTransaction(transactionData);
            }

            async function testRoundAmount() {
                await processTransaction({
                    user_id: currentUser.user_id,
                    recipient_account: '****************',
                    recipient_name: 'Round Amount Test',
                    amount: 50000,
                    transaction_type: 'transfer',
                    description: 'Round amount test',
                    payment_method: 'online'
                });
            }

            async function testFraudTransaction() {
                await processTransaction({
                    user_id: currentUser.user_id,
                    recipient_account: '****************',
                    recipient_name: 'Suspicious Recipient',
                    amount: 150000,
                    transaction_type: 'transfer',
                    description: 'Suspicious high amount transfer',
                    payment_method: 'online',
                    device_info: 'New Device - Unknown Browser'
                });
            }

            // Load transaction history
            async function loadTransactionHistory() {
                try {
                    const response = await fetch(`/api/get_transactions/${currentUser.user_id}`);
                    const data = await response.json();

                    const historyDiv = document.getElementById('transactionHistory');

                    if (data.transactions && data.transactions.length > 0) {
                        historyDiv.innerHTML = data.transactions.map(t => `
                            <div class="transaction-item ${t.is_fraud ? 'fraud' : ''}">
                                <div>
                                    <strong>${t.recipient_name || t.merchant || 'Unknown'}</strong><br>
                                    <small>${new Date(t.timestamp).toLocaleString()}</small><br>
                                    <small>ID: ${t.transaction_id}</small>
                                    ${t.is_fraud ? '<br><span style="color: #dc3545;">🚨 FRAUD BLOCKED</span>' : ''}
                                    ${t.fraud_score > 30 && !t.is_fraud ? '<br><span style="color: #ffc107;">⚠️ UNDER REVIEW</span>' : ''}
                                </div>
                                <div style="text-align: right;">
                                    <strong style="color: ${t.is_fraud ? '#dc3545' : '#28a745'}">
                                        ${t.is_fraud ? 'CANCELLED' : '₹' + t.amount.toLocaleString()}
                                    </strong><br>
                                    <small>${t.status.toUpperCase()}</small>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        historyDiv.innerHTML = '<p>No transactions found.</p>';
                    }
                } catch (error) {
                    document.getElementById('transactionHistory').innerHTML = '<p>Error loading transactions.</p>';
                }
            }

            // Load security alerts
            async function loadSecurityAlerts() {
                try {
                    const response = await fetch(`/api/get_alerts/${currentUser.user_id}`);
                    const data = await response.json();

                    const alertsDiv = document.getElementById('securityAlerts');

                    if (data.alerts && data.alerts.length > 0) {
                        alertsDiv.innerHTML = data.alerts.map(alert => `
                            <div class="alert alert-${alert.severity === 'high' ? 'danger' : alert.severity === 'medium' ? 'warning' : 'success'}">
                                <strong>${alert.title}</strong><br>
                                <small>${new Date(alert.timestamp).toLocaleString()}</small><br>
                                ${alert.message.substring(0, 200)}...
                            </div>
                        `).join('');
                    } else {
                        alertsDiv.innerHTML = '<div class="alert alert-success">No security alerts. Your account is secure!</div>';
                    }
                } catch (error) {
                    document.getElementById('securityAlerts').innerHTML = '<p>Error loading alerts.</p>';
                }
            }
        </script>
    </body>
    </html>
    """
    return dashboard_html

if __name__ == '__main__':
    print("🚀 Starting Real-Time Banking Fraud Detection System...")
    print("🏦 Banking Dashboard: http://localhost:5002/api/fraud_dashboard")
    print("🔍 Features:")
    print("   ✅ Real-time fraud detection")
    print("   ✅ Automatic payment cancellation")
    print("   ✅ Multi-channel user alerts (Email, SMS, Push)")
    print("   ✅ ML-based risk scoring")
    print("   ✅ Rule-based fraud prevention")
    print("   ✅ Transaction monitoring")
    print("   ✅ Security alerts dashboard")
    app.run(debug=True, host='0.0.0.0', port=5002)